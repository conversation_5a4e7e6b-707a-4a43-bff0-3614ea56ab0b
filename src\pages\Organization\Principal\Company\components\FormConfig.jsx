import ZKSearchTree from "../../../ZKSearchTree.jsx";
export const getFormConfig = (t, type, treeSelectRef) => {
  let formConfig = [
    {
      name: "name",
      label: t("principal.principal_name"),
      placeholder: t("principal.enter_principal_name"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("principal.principal_name_required"),
        },
      ],
    },

    {
      name: "email",
      label: t("principal.principal_owner_email"),
      type: "input",
      disabled: type == "editor",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("principal.principal_owner_email_required"),
        },
        {
          type: "email",
          message: t("partner.email_format_error"),
        },
      ],
    },

    {
      codename: "countryCode", // 对应区号字段名
      name: "phone", // 对应电话号码字段名
      label: t("principal.mobile_number"),
      type: "mobile",
      required: true,

      disabled: type == "editor",
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("principal.mobile_number_required"),
        },
        {
          type: "matches",
          matches: /^\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,4}$/,
          message: t("common.common_mobile_format"),
        },
      ],
    },

    {
      name: "areaId",
      required: true,
      custom: true,
      label: t("branch.branch_region"),
      renderingCustomItem: (item, formik) => {
        return (
          <Grid xs={6} pl={3} mt={3}>
            <ZKSearchTree ref={treeSelectRef} formik={formik} {...item} />
          </Grid>
        );
      },

      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("branch.branch_region_required"),
        },
      ],
    },

    {
      name: "address",
      label: t("branch_user.address"),
      type: "address",
      required: true,
      placeholder: t("common.common_input_address"),
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common.common_input_address"),
        },
      ],
    },
  ];

  return formConfig;
};
