// src/layout/MainLayout/components/Drawer.jsx
import React, { useState } from "react";
import { Box, Grid, ListItemButton, ListItemIcon } from "@mui/material";
import ZKLogo from "@/assets/Images/Logo/ZKDIGIMAX.png";
import UserCompany from "../components/UserCompany";
import SwitchLanguage from "../components/SwitchLanguage";
import UserProfile from "../components/UserProfile";
import DrawerContent from "./DrawerContent";
import Scrollbar from "@/components/@extended/Scrollbar";
import SystemLog from "@/assets/Icons/UserSetting.svg?react";
import DataPermissionIcon from "@/assets/Icons/DataPermission.svg?react";
import Manager from "@/assets/menuIcon/manager.svg?react";
import SystemSetting from "@/assets/menuIcon/SystemIcon.svg?react";
import Home from "@/assets/menuIcon/Home.svg?react";
import ApplicationCenter from "@/assets/menuIcon/ApplicationCenter.svg?react";
import Subscription from "@/assets/menuIcon/Subscription.svg?react";
import DeviceIcon from "@/assets/menuIcon/DeviceIcon.svg?react";
import DictManager from "@/assets/menuIcon/DictManage.svg?react";
// import "simplebar/src/simplebar.css";
import { useNavigate } from "react-router-dom";
import { useTheme } from "@mui/material/styles";
import Popover from "@mui/material/Popover";
import {
  bindPopover,
  bindTrigger,
  usePopupState,
} from "material-ui-popup-state/hooks";

import { cn } from "@/utils/cn";

const iconMap = {
  organizationManage: <Manager />,
  systemLog: <SystemLog />,
  systemSetting: <SystemSetting />,
  home: <Home />,
  subscription: <Subscription />,
  applicationCenter: <ApplicationCenter />,
  DataScope: <DataPermissionIcon />,
  DeviceManage: <DeviceIcon />,
  DictManage: <DictManager />,
};

const Drawer = ({ open, width, menuList }) => {
  const getIcons = (key) => iconMap[key] || null;
  const navigate = useNavigate();

  const [data, setData] = useState(null);

  const popupState = usePopupState({
    variant: "popover",
    popupId: "demoPopover",
  });

  const theme = useTheme();
  const iconContainerStyle = {
    width: 36,
    height: 36,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  };

  const handleClick = (item, event) => {
    const firstData = menuList?.find((data) => data.code == item.code);
    setData(firstData);
    if (item?.children) {
      // 使用 popupState 的事件处理，确保正确的定位
      popupState.open(event);
    } else {
      navigate(item.path);
    }
  };

  const handlerSelect = (item) => {
    return Boolean(menuList?.find((code) => code === item.code));
  };

  const handleChildClick = (child) => {
    navigate(child.path);
    popupState.close(); // 点击子菜单后关闭 Popover
  };

  const handleClose = () => {
    popupState.close(); // 关闭 Popover
  };

  const listItemButtonStyle = {
    zIndex: 1201,
    display: "flex",
    justifyContent: "space-between",

    height: "50px",
    alignContent: "center",
    font: `normal normal normal 14px/18px Proxima Nova`,
    color: "#4F4F4F",
    "&:hover": {
      color: "primary.main",

      // bgcolor: "rgba(122, 193, 67, 0.26)",
      bgcolor: "rgba(122, 193, 67, 0.26)",
    },
    "&.Mui-selected": {
      bgcolor: "rgba(122, 193, 67,0.2)",
      color: "primary.main",
      "&::before": {
        content: '""',
        position: "absolute",
        top: 0,
        left: 0,
        bottom: 0,
        width: "4px",
        backgroundColor: theme.palette.primary.main,
      },
    },
  };
  return (
    <Box
      style={{
        width: open ? `${width}px` : "64px",
        position: "absolute",
        top: "20px",
        bottom: "20px",
        zIndex: 2,
        transition: "width 0.3s ease-in-out, box-shadow 0.8s ease-in-out",
        // overflow: "visible",
        display: "flex",
        flexDirection: "column",
        boxSizing: "border-box",
        paddingTop: 2,
        paddingBottom: "10px",
        justifyContent: "space-between",
      }}
      className={cn("bg-white shadow-md", !open ? "" : " rounded-lg ")}>
      <Box>
        {menuList?.map((item, index) => {
          return (
            <Grid key={item.id}>
              {!open && (
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    flexDirection: "column",
                    justifyContent: "space-between",
                    height: "100%",
                    py: 2, // 添加上下内边距
                  }}>
                  <ListItemButton
                    onClick={(event) => handleClick(item, event)}
                    selected={handlerSelect(item)} // 直接调用函数
                    sx={listItemButtonStyle}
                    {...bindTrigger(popupState)}>
                    <ListItemIcon sx={iconContainerStyle}>
                      {getIcons(item.icon)}
                    </ListItemIcon>
                  </ListItemButton>
                </Box>
              )}
            </Grid>
          );
        })}
        {/* 二级菜单 */}
        {data?.children && (
          <Popover
            {...bindPopover(popupState)}
            onClose={handleClose}
            sx={{
              "& .MuiPaper-root": {
                width: "180px",
                marginLeft: "80px",
                borderRadius: "8px",
                boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.2)",
              },
            }}
            anchorOrigin={{
              vertical: "center",
              horizontal: "left",
            }}
            transformOrigin={{
              vertical: "center",
              horizontal: "left",
            }}>
            <Box>
              {data?.children?.map((item, index) => {
                return (
                  <ListItemButton
                    key={index}
                    onClick={() => handleChildClick(item)}
                    sx={{
                      ...listItemButtonStyle,

                      px: open ? 2 : 1,
                    }}>
                    {item?.name}
                  </ListItemButton>
                );
              })}
            </Box>
          </Popover>
        )}
      </Box>

      {!open && (
        <div className="w-full  flex justify-center items-center">
          <UserProfile open={false} />
        </div>
      )}

      {/* 标题栏 */}
      {open && (
        <Box
          sx={{
            flex: "0 0 auto",
            m: 1,
            textAlign: "center",
            padding: "15px 15px 5px 15px",
            opacity: open ? 1 : 0,
            transition: "opacity 0.6s ease-in-out",
          }}>
          <img
            src={ZKLogo}
            alt="Logo"
            style={{ width: "80%", height: "35px" }}
          />
        </Box>
      )}

      {/* 内容区域 */}
      {open && (
        <Box
          sx={{
            flex: "1 1 auto",
            position: "relative",
            overflow: "hidden",
            zIndex: 1,
            opacity: open ? 1 : 0,
            transition: "opacity 0.6s ease-in-out",
          }}>
          <Scrollbar
            xs={{
              "& .simplebar-content": {
                display: "flex",
                flexDirection: "column",
                background: "white",
                border: `1px solid #f4f4f4`,
              },
            }}>
            <DrawerContent />
          </Scrollbar>
        </Box>
      )}

      {/* 底部操作栏 */}
      {open && (
        <Box
          sx={{
            flex: "0 0 auto",
            width: "100%",
            display: open ? "block" : "none",
            background: "white",
            // zIndex: 2,
            px: 1,
            py: 1,
            borderBottomLeftRadius: "10px",
            borderBottomRightRadius: "10px",
          }}>
          <UserProfile open={true} />
          <UserCompany />
          <SwitchLanguage />
          {/* <Notification /> */}
        </Box>
      )}
    </Box>
  );
};

export default Drawer;
