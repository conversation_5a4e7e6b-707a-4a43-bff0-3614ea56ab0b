/**
 * 缓存配置文件
 * 定义应用的缓存策略和预缓存资源
 */

// 预缓存资源列表
export const PRECACHE_RESOURCES = [
  // 核心应用文件
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  
  // 重要的静态资源
  '/assets/images/logo.png',
  '/assets/icons/icon-192x192.png',
  '/assets/icons/icon-512x512.png',
  
  // 关键路由页面
  '/login',
  '/dashboard',
  '/404',
  '/500',
];

// 缓存策略配置
export const CACHE_STRATEGIES = {
  // 静态资源缓存策略
  STATIC_ASSETS: {
    name: 'static-assets-v1',
    pattern: /\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico)$/,
    strategy: 'CacheFirst',
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
    maxEntries: 200,
    description: '静态资源使用缓存优先策略，长期缓存'
  },

  // API请求缓存策略
  API_REQUESTS: {
    name: 'api-requests-v1',
    pattern: /\/api\//,
    strategy: 'NetworkFirst',
    maxAge: 24 * 60 * 60 * 1000, // 1天
    maxEntries: 100,
    networkTimeout: 5000,
    description: 'API请求使用网络优先策略，失败时使用缓存'
  },

  // 动态内容缓存策略
  DYNAMIC_CONTENT: {
    name: 'dynamic-content-v1',
    pattern: /\.(html|json)$/,
    strategy: 'NetworkFirst',
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
    maxEntries: 50,
    networkTimeout: 3000,
    description: '动态内容使用网络优先策略，短期缓存'
  },

  // 字体文件缓存策略
  FONTS: {
    name: 'fonts-v1',
    pattern: /\.(woff|woff2|ttf|eot)$/,
    strategy: 'CacheFirst',
    maxAge: 365 * 24 * 60 * 60 * 1000, // 1年
    maxEntries: 50,
    description: '字体文件使用缓存优先策略，长期缓存'
  },

  // 图片资源缓存策略
  IMAGES: {
    name: 'images-v1',
    pattern: /\.(png|jpg|jpeg|gif|svg|webp|ico)$/,
    strategy: 'CacheFirst',
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
    maxEntries: 200,
    description: '图片资源使用缓存优先策略，中期缓存'
  }
};

// 外部资源缓存配置
export const EXTERNAL_CACHE_CONFIG = [
  {
    name: 'google-fonts-stylesheets',
    pattern: /^https:\/\/fonts\.googleapis\.com\/.*/,
    strategy: 'CacheFirst',
    maxAge: 365 * 24 * 60 * 60 * 1000, // 1年
    maxEntries: 10,
    description: 'Google字体样式表'
  },
  {
    name: 'google-fonts-webfonts',
    pattern: /^https:\/\/fonts\.gstatic\.com\/.*/,
    strategy: 'CacheFirst',
    maxAge: 365 * 24 * 60 * 60 * 1000, // 1年
    maxEntries: 30,
    description: 'Google字体文件'
  },
  {
    name: 'baidu-maps',
    pattern: /^https:\/\/api\.map\.baidu\.com.*/,
    strategy: 'CacheFirst',
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
    maxEntries: 500,
    description: '百度地图API'
  },
  {
    name: 'cdn-resources',
    pattern: /^https:\/\/cdn\..*/,
    strategy: 'CacheFirst',
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30天
    maxEntries: 100,
    description: 'CDN资源'
  }
];

// 缓存清理配置
export const CACHE_CLEANUP_CONFIG = {
  // 自动清理间隔（毫秒）
  cleanupInterval: 24 * 60 * 60 * 1000, // 24小时
  
  // 最大缓存大小（字节）
  maxTotalCacheSize: 100 * 1024 * 1024, // 100MB
  
  // 缓存过期检查间隔（毫秒）
  expirationCheckInterval: 60 * 60 * 1000, // 1小时
  
  // 是否启用自动清理
  autoCleanup: true,
  
  // 清理策略
  cleanupStrategy: 'lru', // 'lru' | 'fifo' | 'size'
};

// 性能监控配置
export const PERFORMANCE_CONFIG = {
  // 是否启用性能监控
  enabled: process.env.NODE_ENV === 'development',
  
  // 监控数据收集间隔（毫秒）
  collectInterval: 30 * 1000, // 30秒
  
  // 性能数据保留时间（毫秒）
  dataRetentionTime: 24 * 60 * 60 * 1000, // 24小时
  
  // 缓存命中率阈值
  hitRateThreshold: {
    good: 80,
    warning: 60,
    poor: 40
  },
  
  // 加载时间阈值（毫秒）
  loadTimeThreshold: {
    fast: 500,
    normal: 1000,
    slow: 2000
  }
};

// 离线支持配置
export const OFFLINE_CONFIG = {
  // 是否启用离线支持
  enabled: true,
  
  // 离线页面
  offlinePage: '/offline.html',
  
  // 离线时显示的消息
  offlineMessage: '您当前处于离线状态，正在显示缓存内容',
  
  // 离线时可访问的页面
  offlinePages: [
    '/',
    '/dashboard',
    '/profile',
    '/settings'
  ],
  
  // 离线时不可访问的页面
  onlineOnlyPages: [
    '/admin',
    '/reports',
    '/analytics'
  ]
};

// PWA配置
export const PWA_CONFIG = {
  // 应用名称
  name: 'ZK DigiMax Application',
  shortName: 'ZK DigiMax',
  
  // 应用描述
  description: 'ZK DigiMax 企业级应用平台',
  
  // 主题色
  themeColor: '#1487CB',
  backgroundColor: '#ffffff',
  
  // 显示模式
  display: 'standalone',
  
  // 屏幕方向
  orientation: 'portrait',
  
  // 启动URL
  startUrl: '/',
  
  // 作用域
  scope: '/',
  
  // 图标配置
  icons: [
    {
      src: '/pwa-192x192.png',
      sizes: '192x192',
      type: 'image/png'
    },
    {
      src: '/pwa-512x512.png',
      sizes: '512x512',
      type: 'image/png'
    },
    {
      src: '/pwa-512x512.png',
      sizes: '512x512',
      type: 'image/png',
      purpose: 'any maskable'
    }
  ]
};

// 开发环境配置
export const DEV_CONFIG = {
  // 是否显示缓存调试信息
  showCacheDebug: true,
  
  // 是否显示性能监控
  showPerformanceMonitor: true,
  
  // 是否启用缓存可视化工具
  enableCacheVisualizer: true,
  
  // 调试日志级别
  logLevel: 'debug', // 'debug' | 'info' | 'warn' | 'error'
  
  // 是否模拟慢网络
  simulateSlowNetwork: false,
  
  // 模拟网络延迟（毫秒）
  networkDelay: 1000
};

// 导出默认配置
export default {
  PRECACHE_RESOURCES,
  CACHE_STRATEGIES,
  EXTERNAL_CACHE_CONFIG,
  CACHE_CLEANUP_CONFIG,
  PERFORMANCE_CONFIG,
  OFFLINE_CONFIG,
  PWA_CONFIG,
  DEV_CONFIG
};
