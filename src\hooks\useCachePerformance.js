import { useState, useEffect, useCallback } from 'react';
import cacheManager from '@/utils/cacheManager';

/**
 * 缓存性能监控Hook
 * 提供缓存命中率、加载时间等性能指标
 */
export const useCachePerformance = () => {
  const [metrics, setMetrics] = useState({
    cacheHitRate: 0,
    totalRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    averageLoadTime: 0,
    cacheSize: '0 B',
    isOnline: navigator.onLine
  });

  const [isMonitoring, setIsMonitoring] = useState(false);

  // 性能数据收集
  const performanceData = {
    requests: [],
    startTime: Date.now()
  };

  // 开始监控
  const startMonitoring = useCallback(() => {
    if (isMonitoring) return;

    setIsMonitoring(true);
    
    // 重置数据
    performanceData.requests = [];
    performanceData.startTime = Date.now();

    // 拦截fetch请求进行监控
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const request = new Request(...args);
      const url = request.url;
      
      try {
        // 检查是否有缓存
        const cachedResponse = await caches.match(request);
        const isCacheHit = !!cachedResponse;
        
        // 执行实际请求
        const response = await originalFetch(...args);
        const endTime = performance.now();
        const loadTime = endTime - startTime;
        
        // 记录性能数据
        performanceData.requests.push({
          url,
          isCacheHit,
          loadTime,
          timestamp: Date.now(),
          status: response.status,
          size: response.headers.get('content-length') || 0
        });
        
        // 更新指标
        updateMetrics();
        
        return response;
      } catch (error) {
        const endTime = performance.now();
        const loadTime = endTime - startTime;
        
        performanceData.requests.push({
          url,
          isCacheHit: false,
          loadTime,
          timestamp: Date.now(),
          status: 0,
          size: 0,
          error: error.message
        });
        
        updateMetrics();
        throw error;
      }
    };

    // 监听在线状态变化
    const handleOnlineChange = () => {
      setMetrics(prev => ({
        ...prev,
        isOnline: navigator.onLine
      }));
    };

    window.addEventListener('online', handleOnlineChange);
    window.addEventListener('offline', handleOnlineChange);

    // 清理函数
    return () => {
      window.fetch = originalFetch;
      window.removeEventListener('online', handleOnlineChange);
      window.removeEventListener('offline', handleOnlineChange);
    };
  }, [isMonitoring]);

  // 停止监控
  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false);
  }, []);

  // 更新性能指标
  const updateMetrics = useCallback(async () => {
    const requests = performanceData.requests;
    const totalRequests = requests.length;
    
    if (totalRequests === 0) return;

    const cacheHits = requests.filter(req => req.isCacheHit).length;
    const cacheMisses = totalRequests - cacheHits;
    const cacheHitRate = (cacheHits / totalRequests) * 100;
    
    const totalLoadTime = requests.reduce((sum, req) => sum + req.loadTime, 0);
    const averageLoadTime = totalLoadTime / totalRequests;

    // 获取缓存大小
    let cacheSize = '0 B';
    try {
      const stats = await cacheManager.getCacheStats();
      cacheSize = stats.totalSize;
    } catch (error) {
      console.warn('Failed to get cache size:', error);
    }

    setMetrics(prev => ({
      ...prev,
      cacheHitRate: Math.round(cacheHitRate * 100) / 100,
      totalRequests,
      cacheHits,
      cacheMisses,
      averageLoadTime: Math.round(averageLoadTime * 100) / 100,
      cacheSize
    }));
  }, []);

  // 获取详细的性能报告
  const getPerformanceReport = useCallback(() => {
    const requests = performanceData.requests;
    const now = Date.now();
    const duration = now - performanceData.startTime;

    // 按时间分组统计
    const timeGroups = {};
    const groupInterval = 60000; // 1分钟间隔

    requests.forEach(req => {
      const groupKey = Math.floor((req.timestamp - performanceData.startTime) / groupInterval);
      if (!timeGroups[groupKey]) {
        timeGroups[groupKey] = {
          cacheHits: 0,
          cacheMisses: 0,
          totalLoadTime: 0,
          requests: 0
        };
      }
      
      const group = timeGroups[groupKey];
      group.requests++;
      group.totalLoadTime += req.loadTime;
      
      if (req.isCacheHit) {
        group.cacheHits++;
      } else {
        group.cacheMisses++;
      }
    });

    // 计算趋势
    const timeSeriesData = Object.entries(timeGroups).map(([key, data]) => ({
      time: parseInt(key) * groupInterval,
      cacheHitRate: (data.cacheHits / data.requests) * 100,
      averageLoadTime: data.totalLoadTime / data.requests,
      requestCount: data.requests
    }));

    // 最慢的请求
    const slowestRequests = [...requests]
      .sort((a, b) => b.loadTime - a.loadTime)
      .slice(0, 10);

    // 最频繁的请求
    const urlCounts = {};
    requests.forEach(req => {
      urlCounts[req.url] = (urlCounts[req.url] || 0) + 1;
    });
    
    const mostFrequentUrls = Object.entries(urlCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([url, count]) => ({ url, count }));

    return {
      summary: metrics,
      duration,
      timeSeriesData,
      slowestRequests,
      mostFrequentUrls,
      totalDataTransferred: requests.reduce((sum, req) => sum + parseInt(req.size || 0), 0)
    };
  }, [metrics]);

  // 重置统计数据
  const resetMetrics = useCallback(() => {
    performanceData.requests = [];
    performanceData.startTime = Date.now();
    
    setMetrics({
      cacheHitRate: 0,
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageLoadTime: 0,
      cacheSize: '0 B',
      isOnline: navigator.onLine
    });
  }, []);

  // 获取缓存建议
  const getCacheRecommendations = useCallback(() => {
    const requests = performanceData.requests;
    const recommendations = [];

    // 分析缓存命中率
    if (metrics.cacheHitRate < 50 && metrics.totalRequests > 10) {
      recommendations.push({
        type: 'warning',
        title: '缓存命中率较低',
        description: `当前缓存命中率为 ${metrics.cacheHitRate}%，建议检查缓存策略配置。`,
        action: '优化缓存策略'
      });
    }

    // 分析加载时间
    if (metrics.averageLoadTime > 1000) {
      recommendations.push({
        type: 'info',
        title: '平均加载时间较长',
        description: `平均加载时间为 ${metrics.averageLoadTime}ms，考虑启用更积极的缓存策略。`,
        action: '启用预缓存'
      });
    }

    // 分析频繁请求
    const urlCounts = {};
    requests.forEach(req => {
      if (!req.isCacheHit) {
        urlCounts[req.url] = (urlCounts[req.url] || 0) + 1;
      }
    });

    const frequentMisses = Object.entries(urlCounts)
      .filter(([, count]) => count > 5)
      .map(([url]) => url);

    if (frequentMisses.length > 0) {
      recommendations.push({
        type: 'suggestion',
        title: '发现频繁未命中的资源',
        description: `有 ${frequentMisses.length} 个资源频繁未命中缓存，建议添加到预缓存列表。`,
        action: '添加预缓存',
        urls: frequentMisses.slice(0, 3)
      });
    }

    return recommendations;
  }, [metrics, performanceData.requests]);

  // 自动开始监控
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      startMonitoring();
    }

    return () => {
      stopMonitoring();
    };
  }, []);

  return {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    resetMetrics,
    getPerformanceReport,
    getCacheRecommendations
  };
};
