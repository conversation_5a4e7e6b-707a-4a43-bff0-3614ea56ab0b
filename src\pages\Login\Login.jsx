import React, { useState, useEffect, useRef } from "react";
import AuthWrapper from "./AuthWrapper";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useTranslation } from "react-i18next";
import { getCaptcha, getCaptchaConfig, userLogin } from "@/service/api/user";
import AnimateButton from "@/components/@extended/AnimateButton";
import LoadingButton from "@mui/lab/LoadingButton";
import LoginBox from "@/components/LoginBox";
import { setToken } from "@/utils/auth";
import { Link as RouterLink, useNavigate } from "react-router-dom";
import ZK_DIGIMAX from "@/assets/Images/Logo/ZKDIGIMAX.png";
import { pxToRem } from "@/utils/zkUtils";
import SvgIcon from "@c/SvgIcon.jsx";
import { toast } from "react-toastify";
import TwoFactorAuthDialog from "@/components/TwoFactorAuthDialog";
import {
  Box,
  Checkbox,
  FormControlLabel,
  FormHelperText,
  Grid,
  IconButton,
  InputAdornment,
  OutlinedInput,
  Stack,
  Typography,
  InputLabel,
  Link as MUILink,
} from "@mui/material";
import TabBorderStyle from "@/assets/Icons/TabBorder.svg";
import ChangeTabStyle from "@/assets/Icons/ChangeTabStyle.svg";
import { useDispatch, useSelector } from "react-redux";
import { setInfoLoaded } from "@/store/reducers/user";
import { captcha } from "@/utils/captcha";
import {
  validTfaPassword,
  resendValidCode,
  switchTfa,
} from "@/service/api/user";
const Login = (props) => {
  const { t } = useTranslation();
  const { setLoginOrForgot } = props;
  const dispatch = useDispatch();
  const towFactorRef = useRef();
  const navigate = useNavigate();
  const [captchaInfo, setCaptchaInfo] = useState({
    captchaEnabled: false,
    height: null,
    img: null,
    uuid: null,
    width: null,
  });

  const [showPassword, setShowPassword] = useState(false);
  const [loginCheckcked, setLoginCheckcked] = useState(true);
  const [loading, setLoading] = useState(false);
  const [userOpen, setUserOpen] = useState(false);
  const [privateOpen, setPrivateOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [open, setOpen] = useState(false);
  const [showTwoFactorDialog, setShowTwoFactorDialog] = useState(false);
  const [showTwoLoading, setShowTwoLoading] = useState(false);
  const [captchaConfig, setCaptchaConfig] = useState({
    enabled: false,
    charLength: 4,
    captchaType: "graph",
  });

  const loginCallback = (resData) => {
    setToken(resData.access_token);
    sessionStorage.setItem("tenantCode", resData.tenantCode);
    sessionStorage.setItem("loginRoleKey", resData.roleKey);
    sessionStorage.setItem("OUTLET_PRODUCT", "1");
    toast.success(resData.message);
    navigate("/dashboard");
  };
  // const userEmail = "<EMAIL>"; // 用户邮箱
  const [userEmail, setUserEmail] = useState(null);
  const [userPassword, setUserPassword] = useState(null);

  // 修改 handleQuickLoginSubmit 函数确保正确传递验证码参数
  const handleQuickLoginSubmit = (values) => {
    setLoading(true);
    let params = {
      ...values,
      username: values.username.trim(),
      password: values.password.trim(),
    };

    if (captchaConfig.enabled) {
      //开启验证码，判断类型
      if (captchaConfig.captchaType === "graph") {
        params.code = values.code.trim();
        params.uuid = captchaInfo.uuid;
        dispatch(setInfoLoaded(false));
        userLogin(params)
          .then((res) => {
            //保存登录后服务端返回的令牌信息
            if (res?.code == "00000000") {
              let resData = res.data;
              loginCallback(resData);
            } else {
              toast.error(res.message);
            }
          })
          .catch((err) => {
            loadCaptcha();
          })
          .finally(() => {
            setLoading(false);
          });
      } else {
        delete params.code;
        delete params.uuid;

        //调用生成滑块验证码
        setAnchorEl(document.getElementById("login-button")); // 设置锚点元素
        setOpen(true); // 打开弹出框
        setTimeout(() => {
          const boxElement = document.getElementById("box");
          if (boxElement) {
            captcha(
              {
                bindEl: "#box",
              },
              {
                sliderTitleText: t("common.common_drag_over"),
                theme: "night",
              },
              async ({ data }) => {
                setOpen(false);
                // setAnchorEl(null);
                dispatch(setInfoLoaded(false));

                userLogin({
                  ...params,
                  uuid: data.id,
                })
                  .then((res) => {
                    //保存登录后服务端返回的令牌信息
                    if (res?.code == "00000000") {
                      let resData = res.data;
                      if (res?.data?.tfaStatus == "1") {
                        setShowTwoFactorDialog(true);
                        setUserEmail(values.username);
                        setUserPassword(values.password);
                      } else {
                        loginCallback(resData);
                      }
                    } else {
                      toast.error(res.message);
                    }
                  })
                  .catch((err) => {
                    loadCaptcha();
                  })
                  .finally(() => {
                    setLoading(false);
                  });
              },
              () => {},
              () => {
                setOpen(false);
                setLoading(false);
                // setAnchorEl(null);
              }
            );
          }
        }, 100);
      }
    }
  };

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };
  // 修改 loadCaptcha 函数来处理验证码加载
  const loadCaptcha = async () => {
    const captchaConfig = await getCaptchaConfig()
      .then((res) => {
        setCaptchaConfig(res.data);
        return Promise.resolve(res.data);
      })
      .catch((err) => {
        return Promise.reject(err);
      });

    if (!captchaConfig?.enabled) {
      return;
    }
    if (captchaConfig.captchaType === "graph") {
      getCaptcha()
        .then((res) => {
          // 为 base64 图片添加正确的头部
          const imgSrc = `data:image/png;base64,${res.data.img}`;

          setCaptchaInfo({
            captchaEnabled: res.data.captchaEnabled,
            img: imgSrc, // 使用添加了头部的 base64 图片
            uuid: res.data.uuid,
            width: 130 || res.data.width,
            height: 45 || res.data.height,
          });

          // 如果有UUID，立即更新表单值
          if (res.data.uuid) {
            quickFormik.setFieldValue("uuid", res.data.uuid);
          }
        })
        .catch((err) => {
          console.error("获取验证码出错:", err);
          toast.error("获取验证码失败，请刷新页面重试");
        });
    }
  };

  useEffect(() => {
    loadCaptcha();
  }, []);

  useEffect(() => {
    if (captchaInfo.uuid) {
      quickFormik.setFieldValue("uuid", captchaInfo.uuid);
    }
  }, [captchaInfo]);

  const quickFormik = useFormik({
    initialValues: {
      grantType: "password",
      username: "",
      password: "",
      code: "",
      uuid: "",
    },
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        handleQuickLoginSubmit(values);
        setStatus({ success: false });
        setSubmitting(false);
      } catch (err) {
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
    validate: (values) => {
      const errors = {};

      if (!values.username.trim()) {
        errors.username = t("common.enter_email");
      }

      if (!values.password.trim()) {
        errors.password = t("common.common_form_password_placeholder");
      }

      if (captchaInfo.captchaEnabled && !values.code.trim()) {
        errors.code = t("login.enter_verification_code");
      }

      return errors;
    },
  });

  // 保留这个 useEffect 来更新 uuid
  useEffect(() => {
    if (captchaInfo.uuid) {
      quickFormik.setFieldValue("uuid", captchaInfo.uuid);
    }
  }, [captchaInfo]);

  // 处理验证码验证
  const handleVerifyCode = (verificationCode) => {
    setShowTwoLoading(true);
    try {
      let params = {
        grantType: "password",
        username: userEmail,
        password: userPassword,
        tfaCode: verificationCode,
      };

      userLogin(params).then((res) => {
        if (res?.code == "00000000") {
          toast.success(res?.message);
          setShowTwoFactorDialog(false);
          loginCallback(res.data);
        } else {
          toast.error(res.message);
        }
      });
    } finally {
      towFactorRef.current.clearCode();
      setShowTwoLoading(false);
    }
  };

  // 处理重新发送验证码
  const handleResendCode = async () => {
    // 模拟API延迟
    const res = await resendValidCode({
      username: userEmail,
      password: userPassword,
    });
    if (res?.code == "00000000") {
      toast.success(res?.message);
    }
  };

  // 关闭弹窗
  const handleCloseDialog = () => {
    setShowTwoFactorDialog(false);
  };

  return (
    <AuthWrapper width={"550px"}>
      <img
        style={{
          height: pxToRem(35),
          margin: "auto",
        }}
        src={ZK_DIGIMAX}></img>

      <Grid sx={{ pt: 2 }}>
        <Grid
          sx={{
            font: " normal normal bold 26px/28px Proxima Nova",
            color: "#222222",
            mb: 2,
          }}>
          {t("home.welcome")}!
        </Grid>

        <form
          noValidate
          onSubmit={quickFormik.handleSubmit}
          onKeyDown={(event) => {
            if (event.key === "Enter") {
              event.preventDefault(); // 防止默认行为
              handleQuickLoginSubmit(quickFormik.values); // 调用登录逻辑
            }
          }}>
          <Grid container mt={2}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel
                    style={{
                      color: "#222222",
                      fontSize: "14px",
                      opacity: 0.8,
                    }}
                    htmlFor={"zkInput_" + name}>
                    {t("common.common_email")}
                  </InputLabel>
                  <Stack
                    sx={{
                      flexGrow: 1,
                      width: "100%",
                    }}>
                    <OutlinedInput
                      sx={{
                        background: "white",
                        height: "45px",

                        font: `normal normal normal 16px/19px Proxima Nova`,
                        color: "#808080",
                      }}
                      id="username-quick-login"
                      type="text"
                      placeholder={t("common.enter_email")}
                      value={quickFormik.values.username}
                      name="username"
                      onBlur={(event) => {
                        quickFormik.setFieldValue(
                          "username",
                          quickFormik.values["username"]
                        );
                        quickFormik.handleBlur(event);
                      }}
                      onChange={(event) => {
                        quickFormik.setFieldValue(
                          "username",
                          quickFormik.values["username"]
                        );
                        quickFormik.handleChange(event);
                      }}
                      fullWidth
                      error={Boolean(
                        quickFormik.touched.username &&
                          quickFormik.errors.username
                      )}
                    />

                    {quickFormik.touched.username &&
                      quickFormik.errors.username && (
                        <FormHelperText
                          error
                          id="standard-weight-helper-text-username-login">
                          {quickFormik.errors.username}
                        </FormHelperText>
                      )}
                  </Stack>
                </Stack>
              </Grid>
              <Grid item xs={12}>
                <Stack spacing={1}>
                  <InputLabel
                    style={{
                      color: "#222222",
                      fontSize: "14px",
                      opacity: 0.8,
                    }}
                    htmlFor={"zkInput_" + name}>
                    {t("common.common_form_password_input")}
                  </InputLabel>
                  <Stack
                    sx={{
                      flexGrow: 1,
                      width: "100%",
                    }}>
                    <OutlinedInput
                      sx={{
                        background: "white",
                        height: "45px",
                        font: `normal normal normal 16px/19px Proxima Nova`,
                        color: "#808080",
                      }}
                      fullWidth
                      error={Boolean(
                        quickFormik.touched.password &&
                          quickFormik.errors.password
                      )}
                      id="-password-login"
                      type={showPassword ? "text" : "password"}
                      value={quickFormik.values.password}
                      name="password"
                      onBlur={quickFormik.handleBlur}
                      onChange={quickFormik.handleChange}
                      onKeyDown={(event) => {
                        if (event.key == "Enter") {
                          event.preventDefault();
                          handleQuickLoginSubmit(quickFormik.values);
                        }
                      }}
                      endAdornment={
                        <InputAdornment position="end">
                          <IconButton
                            aria-label="toggle password visibility"
                            onClick={handleClickShowPassword}
                            onMouseDown={() => {
                              event.preventDefault();
                            }}
                            edge="end"
                            size="large">
                            {showPassword ? (
                              <SvgIcon icon="material-symbols:visibility-outline-rounded"></SvgIcon>
                            ) : (
                              <SvgIcon icon="material-symbols:visibility-off-outline"></SvgIcon>
                            )}
                          </IconButton>
                        </InputAdornment>
                      }
                      placeholder={t("common.common_form_password_placeholder")}
                    />
                    {quickFormik.touched.password &&
                      quickFormik.errors.password && (
                        <FormHelperText
                          error
                          id="standard-weight-helper-text-password-login">
                          {quickFormik.errors.password}
                        </FormHelperText>
                      )}
                  </Stack>
                </Stack>
              </Grid>
              {captchaConfig.enabled &&
                captchaConfig.captchaType === "graph" && (
                  <Grid
                    item
                    xs={12}
                    container
                    direction="row"
                    justifyContent="space-between"
                    alignItems="flex-start">
                    {/* 改为 flex-start 使元素顶部对齐 */}
                    <Grid item xs={8}>
                      <Stack spacing={1}>
                        <InputLabel
                          style={{
                            color: "#222222",
                            fontSize: "14px",
                            opacity: 0.8,
                          }}
                          htmlFor="code-login">
                          {t("common.common_form_verification_input")}
                        </InputLabel>
                        <OutlinedInput
                          sx={{
                            background: "white",
                            height: "45px",
                          }}
                          id="code-login"
                          type="text"
                          name="code"
                          value={quickFormik.values.code}
                          onBlur={quickFormik.handleBlur}
                          onChange={quickFormik.handleChange}
                          placeholder={
                            t("common.common_form_code_placeholder") ||
                            "请输入验证码"
                          }
                          fullWidth
                          error={Boolean(
                            quickFormik.touched.code && quickFormik.errors.code
                          )}
                        />
                        {quickFormik.touched.code &&
                          quickFormik.errors.code && (
                            <FormHelperText
                              error
                              id="standard-weight-helper-text-code-login">
                              {quickFormik.errors.code}
                            </FormHelperText>
                          )}
                      </Stack>
                    </Grid>
                    <Grid item xs={4} container justifyContent="flex-end">
                      <Stack
                        spacing={1}
                        sx={{ width: "100%", alignItems: "flex-end" }}>
                        <InputLabel
                          style={{
                            color: "#222222",
                            fontSize: "14px",
                            opacity: 0.8,
                            visibility: "hidden", // 隐藏但保留空间
                          }}>
                          {t("common.common_form_code_placeholder") || "验证码"}
                        </InputLabel>
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            height: "45px",
                          }}>
                          <img
                            style={{
                              border: "1px solid #f0f0f0",
                              cursor: "pointer",
                              height: captchaInfo.height
                                ? `${captchaInfo.height}px`
                                : "40px",
                              width: captchaInfo.width
                                ? `${captchaInfo.width}px`
                                : "auto",
                            }}
                            src={captchaInfo.img}
                            onClick={() => {
                              loadCaptcha();
                            }}
                            alt="验证码"
                          />
                        </Box>
                      </Stack>
                    </Grid>
                  </Grid>
                )}
            </Grid>

            <Grid item xs={12} mt={2}>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center">
                <Grid></Grid>
                <MUILink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    setLoginOrForgot("1");
                  }}
                  sx={{
                    fontSize: "14px",
                    color: "#1487CB",
                    textDecoration: "none",
                    fontFamily: "Proxima Nova",
                    "&:hover": {
                      textDecoration: "underline",
                    },
                  }}>
                  {t("menu.common_forgot")}
                </MUILink>
              </Stack>
            </Grid>

            <Grid item xs={12} mt={1}>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center">
                <FormControlLabel
                  control={
                    <Checkbox
                      sx={{
                        color: "linear-gradient(90deg, #78BC27, #1487CB)",
                        font: `normal normal normal 14px/16px Proxima Nova`,
                        "&.Mui-checked": {
                          color: "linear-gradient(90deg, #78BC27, #1487CB)",
                        },
                      }}
                      checked={loginCheckcked}
                      onChange={(event) => {
                        // 设置到缓存中
                        setAgree(event.target.checked);
                        // 状态变更
                        setLoginCheckcked(event.target.checked);
                      }}
                      name="checked"
                      color="primary"
                      size="small"
                    />
                  }
                  label={
                    <Typography
                      variant="h5"
                      color="DimGray"
                      sx={{
                        font: `normal normal normal 14px/16px Proxima Nova`,
                        whiteSpace: "nowrap",
                      }}>
                      {t("common.common_form_agree")}
                      <MUILink
                        href="#"
                        color={"#1487CB"}
                        onClick={(e) => {
                          e.preventDefault();
                          setUserOpen(true);
                        }}>
                        {t("common.common_user_agree")}
                      </MUILink>
                      {t("common.common_and")}
                      <MUILink
                        href="#"
                        color={"#1487CB"}
                        onClick={(e) => {
                          e.preventDefault();
                          setPrivateOpen(true);
                          // userAgreeRef.current.handleOpen();
                        }}>
                        {t("common.common_private_agree")}
                      </MUILink>
                    </Typography>
                  }
                />
              </Stack>
            </Grid>
            <Grid item xs={12} mt={2}>
              <AnimateButton>
                <LoadingButton
                  sx={{
                    background: "linear-gradient(90deg, #78BC27, #1487CB)",
                    height: "45px",
                    font: `normal normal normal 14px/16px Proxima Nova`,
                  }}
                  loading={loading}
                  disableElevation
                  disabled={quickFormik.isSubmitting}
                  fullWidth
                  size="large"
                  type="submit"
                  variant="contained"
                  color="primary">
                  {t("common.common_login")}
                </LoadingButton>
              </AnimateButton>
            </Grid>
          </Grid>
        </form>
      </Grid>
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={() => {
          setOpen(false);
          setLoading(false);
          // setAnchorEl(null);
        }}
        anchorOrigin={{
          vertical: "center",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "center",
          horizontal: "center",
        }}
        BackdropProps={{
          sx: {
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            backdropFilter: "blur(3px)",
          },
        }}
        PaperProps={{
          sx: {
            borderRadius: "12px",
            overflow: "hidden",
            border: "none",
            boxShadow: "0 10px 25px rgba(0, 0, 0, 0.15)",
            backgroundColor: "transparent",
          },
        }}>
        <Paper
          elevation={0}
          sx={{
            p: 0,
            borderRadius: "12px",
            overflow: "hidden",
            backgroundColor: "transparent",
          }}>
          <div
            id="box"
            style={{
              width: "320px",
              height: "320px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              boxShadow: "0",
            }}></div>
        </Paper>
      </Popover>

      <TwoFactorAuthDialog
        ref={towFactorRef}
        open={showTwoFactorDialog}
        onClose={handleCloseDialog}
        onVerify={handleVerifyCode}
        email={userEmail}
        loading={showTwoLoading}
        onResendCode={handleResendCode}></TwoFactorAuthDialog>
    </AuthWrapper>
  );
};
export default Login;

const ChangeTab = ({ loginType, setLoginType }) => {
  const TabStyle = {
    font: `normal normal normal 20px/24px Proxima Nova`,
    color: "#222222",
    width: "340px",
    height: "36px",
    textAlign: "center",
    position: "relative",
    cursor: "pointer",
  };

  const SingleStyle0 = {
    content: "''",
    position: "absolute",
    bottom: 0,
    left: 0,
    width: "100%",
    height: "4px", // 伪元素的高度
    backgroundImage: `url(${ChangeTabStyle})`,
    backgroundRepeat: "no-repeat",
    backgroundPosition: "bottom center",
    backgroundSize: "cover", // 根据需要调整
    transition: "all 1.6s ease", // 添加过渡效果
  };

  const SingleStyle1 = {
    content: "''",
    position: "absolute",
    bottom: 0,
    left: 0,
    width: "100%",
    height: "4px", // 伪元素的高度
    backgroundImage: `url(${TabBorderStyle})`,
    backgroundRepeat: "no-repeat",
    backgroundPosition: "bottom center",
    backgroundSize: "cover", // 根据需要调整
    transition: "all 1.6s ease", // 添加过渡效果
  };

  return (
    <Grid
      sx={{
        display: "flex",
        justifyContent: "center",
        width: "300px",
        margin: "auto",
      }}>
      <Grid sx={TabStyle} onClick={() => setLoginType("0")}>
        <span>Email</span>
        <div style={loginType == "0" ? SingleStyle1 : SingleStyle0} />
      </Grid>
      <Grid sx={TabStyle} onClick={() => setLoginType("1")}>
        <span>Mobile</span>
        <div style={loginType == "1" ? SingleStyle1 : SingleStyle0} />
      </Grid>
    </Grid>
  );
};

{
  /* <Grid
  sx={{
    display: "flex",
    justifyContent: "center",
    alignContent: "center",
    font: `normal normal normal 14px/16px Proxima Nova`,
  }}>
  <RouterLink
    to="/forgot_password"
    style={{
      color: "#1487CB",
      textDecoration: "none",
      fontWeight: "bold",
      marginTop: "20px",
    }}>
    Forgot Password
  </RouterLink>

  <span
    onClick={() => setLoginOrForgot("1")}
    style={{
      color: "#1487CB",
      textDecoration: "none",
      fontWeight: "bold",
      marginTop: "20px",
      cursor: "pointer",
    }}>
    {t("menu.common_forgot")}
  </span>
</Grid>; */
}
