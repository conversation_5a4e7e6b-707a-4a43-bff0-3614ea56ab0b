// 异常处理组件入口文件
export { default as ErrorBoundary } from './ErrorBoundary';
export { default as NotFound } from './NotFound';

// 创建异常路由配置
export const exceptionRoutes = [
  {
    path: "/404",
    component: () => import("./NotFound"),
    meta: {
      title: "页面未找到",
      i18n: "page_not_found",
      needLogin: false,
      id: "404_page_route",
    },
  },
  {
    path: "/403",
    component: () => import("./PermissionError"),
    meta: {
      title: "访问被拒绝",
      i18n: "permission_denied",
      needLogin: false,
      id: "403_page_route",
    },
  },
  {
    path: "/500",
    component: () => import("./ServerError"),
    meta: {
      title: "服务器错误",
      i18n: "server_error",
      needLogin: false,
      id: "500_page_route",
    },
  },

];

// 错误处理工具函数
export const handleRouteError = (error, errorInfo) => {
  console.error('Route Error:', error, errorInfo);

  // 检查是否为JavaScript语法错误，如果是则不跳转页面
  const isJavaScriptError =
    error?.name === 'SyntaxError' ||
    error?.name === 'ReferenceError' ||
    error?.name === 'TypeError' ||
    error?.message?.includes('is not defined') ||
    error?.message?.includes('not defined') ||
    error?.message?.includes('Syntax') ||
    error?.message?.includes('Unexpected');

  if (isJavaScriptError) {
    console.warn('🚫 JavaScript语法错误不跳转页面，由ErrorBoundary处理:', error);
    return; // 不跳转任何页面
  }

  // 只处理真正的权限和服务器错误
  if (error.message?.includes('403') || error.message?.includes('Forbidden')) {
    console.log('🔄 权限错误，跳转到403页面');
    window.location.href = '/403';
  } else if (error.message?.includes('500') || error.message?.includes('Internal Server Error')) {
    console.log('🔄 服务器错误，跳转到500页面');
    window.location.href = '/500';
  } else {
    // 其他错误（包括网络错误、Chunk加载错误）不跳转，让ErrorBoundary处理
    console.warn('🚫 错误类型不跳转页面，由ErrorBoundary处理:', error);
  }
};

// 网络状态检测
export const checkNetworkStatus = () => {
  return {
    isOnline: navigator.onLine,
    connection: navigator.connection || navigator.mozConnection || navigator.webkitConnection,
    userAgent: navigator.userAgent,
  };
};

// 错误上报函数
export const reportError = (error, errorInfo, additionalData = {}) => {
  const errorReport = {
    message: error?.message,
    stack: error?.stack,
    componentStack: errorInfo?.componentStack,
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent,
    networkStatus: checkNetworkStatus(),
    ...additionalData,
  };

  // 发送到错误监控服务
  if (process.env.NODE_ENV === 'production') {
    // 这里可以集成Sentry、LogRocket等错误监控服务
    console.log('Error Report:', errorReport);

    // 示例：发送到后端API
    // fetch('/api/errors', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(errorReport)
    // }).catch(console.error);
  } else {
    console.error('Development Error Report:', errorReport);
  }
};

// 导入组件用于默认导出
import ErrorBoundaryComponent from './ErrorBoundary';
import NotFoundComponent from './NotFound';

export default {
  ErrorBoundary: ErrorBoundaryComponent,
  NotFound: NotFoundComponent,
  exceptionRoutes,
  handleRouteError,
  checkNetworkStatus,
  reportError,
};
