import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import App from "./App.jsx";
import { BrowserRouter as Router } from "react-router-dom";
import { Provider as ReduxProvider } from "react-redux";
import { store } from "./store";
import "@/styles/simplebar.css";
// import "./rem.js";
import "virtual:svg-icons-register";
import cacheManager from "@/utils/cacheManager";

// 初始化缓存管理器
cacheManager.init().then((success) => {
  if (success) {
    console.log("✅ Cache Manager initialized successfully");
  } else {
    console.warn("⚠️ Cache Manager initialization failed");
  }
});

createRoot(document.getElementById("root")).render(
  // <StrictMode>
  <ReduxProvider store={store}>
    <Router>
      <App />
    </Router>

    <div id="sub-app-container"></div>
  </ReduxProvider>
  // </StrictMode>
);
