#!/usr/bin/env node

/**
 * 缓存系统部署脚本
 * 用于构建后的缓存配置验证和优化
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const DIST_DIR = path.join(__dirname, '../dist');
const SW_FILE = path.join(DIST_DIR, 'sw.js');
const MANIFEST_FILE = path.join(DIST_DIR, 'manifest.json');

console.log('🚀 开始部署缓存系统...\n');

// 1. 检查构建文件
function checkBuildFiles() {
  console.log('📁 检查构建文件...');
  
  if (!fs.existsSync(DIST_DIR)) {
    console.error('❌ 构建目录不存在，请先运行 npm run build');
    process.exit(1);
  }

  const requiredFiles = ['index.html', 'sw.js', 'manifest.json'];
  const missingFiles = requiredFiles.filter(file => 
    !fs.existsSync(path.join(DIST_DIR, file))
  );

  if (missingFiles.length > 0) {
    console.error(`❌ 缺少必要文件: ${missingFiles.join(', ')}`);
    process.exit(1);
  }

  console.log('✅ 构建文件检查通过\n');
}

// 2. 生成文件清单
function generateFileManifest() {
  console.log('📋 生成文件清单...');
  
  const files = [];
  
  function scanDirectory(dir, baseDir = '') {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const relativePath = path.join(baseDir, item).replace(/\\/g, '/');
      
      if (fs.statSync(fullPath).isDirectory()) {
        scanDirectory(fullPath, relativePath);
      } else {
        const stats = fs.statSync(fullPath);
        const content = fs.readFileSync(fullPath);
        const hash = crypto.createHash('md5').update(content).digest('hex');
        
        files.push({
          path: '/' + relativePath,
          size: stats.size,
          hash: hash,
          lastModified: stats.mtime.toISOString()
        });
      }
    });
  }
  
  scanDirectory(DIST_DIR);
  
  // 按文件大小排序
  files.sort((a, b) => b.size - a.size);
  
  const manifestPath = path.join(DIST_DIR, 'file-manifest.json');
  fs.writeFileSync(manifestPath, JSON.stringify({
    generated: new Date().toISOString(),
    totalFiles: files.length,
    totalSize: files.reduce((sum, file) => sum + file.size, 0),
    files: files
  }, null, 2));
  
  console.log(`✅ 生成文件清单: ${files.length} 个文件\n`);
  return files;
}

// 3. 优化Service Worker
function optimizeServiceWorker() {
  console.log('⚙️ 优化Service Worker...');
  
  if (!fs.existsSync(SW_FILE)) {
    console.warn('⚠️ Service Worker 文件不存在，跳过优化');
    return;
  }
  
  let swContent = fs.readFileSync(SW_FILE, 'utf8');
  
  // 添加版本信息
  const version = require('../package.json').version;
  const buildTime = new Date().toISOString();
  
  const versionInfo = `
// Service Worker Version: ${version}
// Build Time: ${buildTime}
// Auto-generated optimization
`;
  
  swContent = versionInfo + swContent;
  
  // 添加缓存版本控制
  swContent = swContent.replace(
    /const CACHE_NAME = ['"]([^'"]+)['"]/,
    `const CACHE_NAME = '$1-${version}'`
  );
  
  fs.writeFileSync(SW_FILE, swContent);
  console.log('✅ Service Worker 优化完成\n');
}

// 4. 验证PWA配置
function validatePWAConfig() {
  console.log('🔍 验证PWA配置...');
  
  if (!fs.existsSync(MANIFEST_FILE)) {
    console.warn('⚠️ manifest.json 不存在');
    return;
  }
  
  const manifest = JSON.parse(fs.readFileSync(MANIFEST_FILE, 'utf8'));
  
  const requiredFields = ['name', 'short_name', 'start_url', 'display', 'theme_color'];
  const missingFields = requiredFields.filter(field => !manifest[field]);
  
  if (missingFields.length > 0) {
    console.warn(`⚠️ manifest.json 缺少字段: ${missingFields.join(', ')}`);
  }
  
  // 检查图标
  if (!manifest.icons || manifest.icons.length === 0) {
    console.warn('⚠️ manifest.json 缺少图标配置');
  } else {
    manifest.icons.forEach(icon => {
      const iconPath = path.join(DIST_DIR, icon.src.replace(/^\//, ''));
      if (!fs.existsSync(iconPath)) {
        console.warn(`⚠️ 图标文件不存在: ${icon.src}`);
      }
    });
  }
  
  console.log('✅ PWA配置验证完成\n');
}

// 5. 生成缓存报告
function generateCacheReport(files) {
  console.log('📊 生成缓存报告...');
  
  const report = {
    summary: {
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      generatedAt: new Date().toISOString()
    },
    categories: {},
    recommendations: []
  };
  
  // 按文件类型分类
  files.forEach(file => {
    const ext = path.extname(file.path).toLowerCase();
    const category = getCategoryByExtension(ext);
    
    if (!report.categories[category]) {
      report.categories[category] = {
        count: 0,
        totalSize: 0,
        files: []
      };
    }
    
    report.categories[category].count++;
    report.categories[category].totalSize += file.size;
    report.categories[category].files.push(file);
  });
  
  // 生成建议
  Object.entries(report.categories).forEach(([category, data]) => {
    if (category === 'images' && data.totalSize > 5 * 1024 * 1024) {
      report.recommendations.push({
        type: 'warning',
        category: 'images',
        message: `图片文件总大小 ${formatBytes(data.totalSize)} 较大，建议优化压缩`
      });
    }
    
    if (category === 'scripts' && data.count > 20) {
      report.recommendations.push({
        type: 'info',
        category: 'scripts',
        message: `JavaScript文件数量 ${data.count} 较多，建议合并打包`
      });
    }
  });
  
  const reportPath = path.join(DIST_DIR, 'cache-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  // 输出摘要
  console.log('📈 缓存报告摘要:');
  console.log(`   总文件数: ${report.summary.totalFiles}`);
  console.log(`   总大小: ${formatBytes(report.summary.totalSize)}`);
  
  Object.entries(report.categories).forEach(([category, data]) => {
    console.log(`   ${category}: ${data.count} 个文件, ${formatBytes(data.totalSize)}`);
  });
  
  if (report.recommendations.length > 0) {
    console.log('\n💡 优化建议:');
    report.recommendations.forEach(rec => {
      console.log(`   ${rec.type === 'warning' ? '⚠️' : 'ℹ️'} ${rec.message}`);
    });
  }
  
  console.log('\n✅ 缓存报告生成完成\n');
}

// 6. 生成部署说明
function generateDeploymentGuide() {
  console.log('📝 生成部署说明...');
  
  const guide = `# 部署说明

## 缓存系统部署清单

### 1. 文件检查
- ✅ 所有必要文件已生成
- ✅ Service Worker 已优化
- ✅ PWA 配置已验证

### 2. 服务器配置

#### Nginx 配置示例
\`\`\`nginx
# Service Worker 缓存配置
location /sw.js {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
}

# Manifest 文件配置
location /manifest.json {
    add_header Cache-Control "public, max-age=86400";
}

# 静态资源长期缓存
location ~* \\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# HTML 文件短期缓存
location ~* \\.html$ {
    expires 1h;
    add_header Cache-Control "public, must-revalidate";
}
\`\`\`

#### Apache 配置示例
\`\`\`apache
# Service Worker
<Files "sw.js">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
</Files>

# 静态资源
<FilesMatch "\\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 year"
    Header set Cache-Control "public, immutable"
</FilesMatch>
\`\`\`

### 3. HTTPS 要求
- Service Worker 需要 HTTPS 环境
- 确保 SSL 证书配置正确

### 4. 验证部署
1. 打开浏览器开发者工具
2. 检查 Application > Service Workers
3. 验证缓存存储正常工作
4. 测试离线功能

### 5. 监控建议
- 定期检查缓存命中率
- 监控 Service Worker 更新
- 关注用户离线体验反馈

生成时间: ${new Date().toISOString()}
`;
  
  const guidePath = path.join(DIST_DIR, 'DEPLOYMENT.md');
  fs.writeFileSync(guidePath, guide);
  
  console.log('✅ 部署说明生成完成\n');
}

// 工具函数
function getCategoryByExtension(ext) {
  const categories = {
    scripts: ['.js', '.mjs'],
    styles: ['.css'],
    images: ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.ico'],
    fonts: ['.woff', '.woff2', '.ttf', '.eot'],
    data: ['.json', '.xml'],
    documents: ['.html', '.htm']
  };
  
  for (const [category, extensions] of Object.entries(categories)) {
    if (extensions.includes(ext)) {
      return category;
    }
  }
  
  return 'other';
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 主执行流程
async function main() {
  try {
    checkBuildFiles();
    const files = generateFileManifest();
    optimizeServiceWorker();
    validatePWAConfig();
    generateCacheReport(files);
    generateDeploymentGuide();
    
    console.log('🎉 缓存系统部署完成！');
    console.log('\n📋 生成的文件:');
    console.log('   - file-manifest.json (文件清单)');
    console.log('   - cache-report.json (缓存报告)');
    console.log('   - DEPLOYMENT.md (部署说明)');
    console.log('\n🚀 现在可以部署到生产环境了！');
    
  } catch (error) {
    console.error('❌ 部署失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkBuildFiles,
  generateFileManifest,
  optimizeServiceWorker,
  validatePWAConfig,
  generateCacheReport,
  generateDeploymentGuide
};
