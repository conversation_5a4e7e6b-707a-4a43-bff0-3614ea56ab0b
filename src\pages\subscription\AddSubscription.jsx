import React, { useEffect, useState } from "react";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import { useTranslation } from "react-i18next";
import Subscription from "./components/Subscription.jsx";
import { useFormik } from "formik";
import { getInitialValues } from "./js/InitialValues";
import { useLocation, useNavigate } from "react-router-dom";
import { createValidation } from "@/components/Config/validationUtils.js";
import AccountInfo from "./components/AccountInfo";
import ContactInfo from "./components/ContactInfo";
import UploadImage from "@c/UploadImage.jsx";

import {
  addSubscription,
  editSubscription,
} from "@/service/api/subscription.js";
import { toast } from "react-toastify";

function AddSubscription() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { state } = useLocation();
  const [loading, setLoading] = useState(false);
  const [deviceConfig, setDeviceConfig] = useState([]); // 设备数量控制
  const [accountInfoConfig, setAccountInfoConfig] = useState([]);
  const [contactInfo, setContactInfo] = useState([]);
  const [imageUrl, setImageUrl] = useState(null);
  const [fileUrl, setFileUrl] = useState(null);

  //  上传 logo
  const handleUpload = (file, setImageUrl, setFileUrl) => {
    setFileUrl(file);
    const reader = new FileReader();
    reader.onload = (e) => {
      setImageUrl(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  const formConfig = [
    ...deviceConfig,
    ...accountInfoConfig,
    ...(state?.type !== 1 && state?.type !== "add" ? contactInfo : []),
  ];

  const formik = useFormik({
    initialValues: getInitialValues(state),
    enableReinitialize: true, // 允许重新初始化
    validationSchema: createValidation(formConfig),
    onSubmit: async (values) => {
      try {
        let params = {
          ...values,
          logoFile: fileUrl,
          type: state?.type,
          activateTime: dayjs(values.activateTime).format("YYYY-MM-DD"),
          expiraTime: dayjs(values.expiraTime).format("YYYY-MM-DD"),
          longitude: values.lng,
          latitude: values.lat,
        };

        setLoading(true);

        // 定义操作映射
        const actionMap = {
          add: addSubscription,
          default: editSubscription,
        };

        // 根据 state.type 查找相应的处理函数
        const action = actionMap[state?.type] || actionMap.default;

        // 调用相应的操作函数
        const res = await action(params);
        toast.success(res?.message);
        navigate("/scriptionRoute");
      } catch (error) {
        // toast.error("An error occurred. Please try again.");
        console.error("新增订阅失败", error); // 捕获错误并打印
      } finally {
        setLoading(false); // 无论成功与否都要停止加载状态
      }
    },
  });

  useEffect(() => {
    setImageUrl(state?.data?.attachmentUrl);
    if (state?.type == "add") {
      formik.setFieldValue("packageId", state?.id);
      formik.setFieldValue("deviceCount", state?.deviceCount);
    }
  }, [state]);
  //订阅类型  1升级变更 2增加设备 3续订（增加时间）
  return (
    <RightViewLayout
      navigateBack={"-1"}
      title={
        state?.type == "add"
          ? t("subscription.add_subscription")
          : state?.type == 1
          ? t("subscription.update_subscription")
          : state?.type == 2
          ? t("subscription.add_device")
          : state?.type == 3
          ? t("subscription.extend_subscription")
          : t("subscription.unknown_type") // 其他未知类型
      }
      handleSubmit={formik.handleSubmit}
      handleCancle={() => {
        navigate(-1);
      }}
      loading={loading}>
      <Subscription
        formik={formik}
        deviceConfig={deviceConfig}
        setDeviceConfig={setDeviceConfig}></Subscription>
      <Grid mt={6}>
        <AccountInfo
          formik={formik}
          accountInfoConfig={accountInfoConfig}
          setAccountInfoConfig={setAccountInfoConfig}></AccountInfo>
      </Grid>

      {(state?.type == "1" || state?.type == "add") && (
        <Grid mt={6}>
          <ContactInfo
            formik={formik}
            contactInfo={contactInfo}
            setContactInfo={setContactInfo}></ContactInfo>
        </Grid>
      )}

      {state?.type == "add" && (
        <Grid
          container
          sx={{
            display: "flex",
            justifyContent: "space-between",
            mt: 3,
          }}>
          <Grid item xs={6}>
            <UploadImage
              label={t("common.common_company_logo")}
              imageUrl={imageUrl}
              // required
              setImageUrl={setImageUrl}
              handleUpload={(file) => {
                handleUpload(file, setImageUrl, setFileUrl);
              }}></UploadImage>
          </Grid>
        </Grid>
      )}
    </RightViewLayout>
  );
}

export default AddSubscription;
