import DeleteIcon from "@/assets/Icons/DeteleIcon.svg?react";
import { useTranslation } from "react-i18next";
import { useEffect, useState } from "react";
import { queryProduct, deleteProduct, saveProduct } from "@s/api/productType";
import AddIcon from "@/assets/Icons/Plus icon.svg?react";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import { toast } from "react-toastify";
import ZkDialog from "@c/ZKDialog";
import CustomInput from "@c/CustInput";
import { useConfirm } from "@/components/zkconfirm";
import Scrollbar from "@/components/@extended/Scrollbar";
import AuthButton from "@/components/AuthButton";

function index(props) {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [addOpen, setAddOpen] = useState(false);
  const [data, setData] = useState([]);
  const [productName, setProductName] = useState("");
  const confirmFn = useConfirm();

  const getLoadData = () => {
    queryProduct().then((responese) => {
      if (responese.code == "00000000") {
        setData(responese?.data);
      } else {
        setData([]);
      }
    });
  };
  useEffect(() => {
    getLoadData();
  }, []);

  const handlerAddProduct = () => {
    let params = {
      name: productName,
    };

    saveProduct(params).then((res) => {
      toast.success(res?.message);
      getLoadData();
      setAddOpen(false);
    });
  };

  const handlerDeteleProduct = (ids) => {
    deleteProduct(ids).then((res) => {
      setOpen(false);
      toast.success(res?.message);
      getLoadData();
    });
  };
  const handleDelete = (field) => {
    confirmFn({
      title: t("删除警告"),
      confirmationText: t("确认"),
      cancellationText: t("取消"),
      description: t("确认删除商品类型？"),
    }).then(() => {
      handlerDeteleProduct(field?.id);
    });
  };

  return (
    <RightViewLayout
      isShowSaveButton={false}
      title={t("product_info.title")}
      height={"100%"}>
      <Scrollbar
        xs={{
          "& .simplebar-content": {
            display: "flex",
            flexDirection: "column",
            background: "white",
            border: `1px solid #f4f4f4`,
          },
        }}>
        <div className="w-full h-full p-4  text-[14px]">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-4">
            {/* Add Button */}

            {/* 
            
                       <AuthButton button="system:product_type:save">
              <div className="col-span-1">
                <div
                  className="border flex py-4 px-1 items-center  h-15 border-solid text-base truncate border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50"
                  onClick={() => setAddOpen(true)}>
                  <Button className="px-2">
                    <AddIcon />
                  </Button>

                  <span>{t("product_info.add_product_element")}</span>
                </div>
              </div>
            </AuthButton>
            */}

            <AuthButton button="system:product_type:save">
              <div className="col-span-1 mb-4">
                <div
                  className="w-full h-20 border border-gray-200 rounded-lg p-4 cursor-pointer hover:bg-gray-50"
                  onClick={() => setAddOpen(true)}>
                  <div className="flex items-center h-full">
                    <AddIcon className="ml-3 mr-5" />
                    <span className="text-lg font-medium text-gray-700 opacity-80">
                      {t("outlet_info.add_outlet_element")}
                    </span>
                  </div>
                </div>
              </div>
            </AuthButton>

            {/* Product Cards */}
            {data?.map((field) => (
              <div
                key={field?.id}
                className="col-span-1 h-20 mb-4 text-gray-500">
                <div className="w-full h-full border border-gray-200 rounded-lg p-4 hover:shadow-md">
                  <div className="flex justify-between items-center h-full">
                    <span className="text-lg font-medium">{field.name}</span>

                    <AuthButton button="system:product_type:delete">
                      <button
                        onClick={() => handleDelete(field)}
                        className="text-red-500 hover:text-red-700">
                        <DeleteIcon />
                      </button>
                    </AuthButton>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Scrollbar>
      <ZkDialog
        title={t("product_info.add_product_element")}
        open={addOpen}
        setOpen={setAddOpen}
        maxWidth="xs"
        handlerSubmit={handlerAddProduct}>
        <CustomInput
          name="name"
          placeholder={t("branch.enter_name")}
          handleChange={(e) => {
            setProductName(e.target.value);
          }}></CustomInput>
      </ZkDialog>
    </RightViewLayout>
  );
}

export default index;

// /**
//  *
//  * @param 删除商品类型
//  * @returns
//  */
// const DeleteDialog = ({ open, setOpen, ids, getLoadData }) => {
//   const { t } = useTranslation();
//   const handlerDeteleProduct = () => {
//     deleteProduct(ids).then((res) => {
//       setOpen(false);
//       toast.success(res?.message);
//       getLoadData();
//     });
//   };

//   return (
//     <Dialog open={open} onClose={() => setOpen(false)} maxWidth={false}>
//       <DialogTitle className="flex items-center gap-2 text-[18px] font-blod">
//         <SvgIcon icon={"carbon:warning"} />
//         <div className="font-blod-[600]">{t("删除警告")}</div>
//       </DialogTitle>
//       <DialogContent className="w-[450px]">
//         <div className="text-[16px] text-gray-500">{t("确认删除商品类型？")}</div>
//       </DialogContent>
//       <DialogActions>
//         <Box display="flex" justifyContent="center">
//           <Button
//             variant="outlined"
//             color="primary"
//             style={{
//               fontSize: "normal normal normal 14px / 22px Roboto",
//             }}
//             onClick={() => setOpen(false)}
//             sx={{ marginRight: 2 }}
//           >
//             {t("取消")}
//           </Button>

//           <Button
//             variant="contained"
//             color="primary"
//             style={{
//               background:
//                 "transparent linear-gradient(270deg, #1487CA 0%, #78BC27 100%)",
//               color: "#FFFFFF",
//               fontSize: "normal normal normal 14px / 22px Roboto",
//               width: "80px",
//             }}
//             onClick={handlerDeteleProduct}
//           >
//             {t("确认")}
//           </Button>
//         </Box>
//       </DialogActions>
//     </Dialog>
//   );
// };
