<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线模式 - ZK DigiMax</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1487CB 0%, #78BC27 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .offline-container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }

        .offline-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .offline-message {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .offline-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.5);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .network-status {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .status-offline {
            background: #ff6b6b;
        }

        .status-online {
            background: #51cf66;
        }

        .cache-info {
            margin-top: 1rem;
            font-size: 0.8rem;
            opacity: 0.7;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @media (max-width: 480px) {
            .offline-container {
                margin: 1rem;
                padding: 1.5rem;
            }

            .offline-title {
                font-size: 1.5rem;
            }

            .offline-actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">
            📡
        </div>
        
        <h1 class="offline-title">您当前处于离线状态</h1>
        
        <p class="offline-message">
            网络连接似乎出现了问题，但不用担心！我们已经为您缓存了一些内容，您仍然可以浏览部分页面。
        </p>

        <div class="offline-actions">
            <button class="btn btn-primary" onclick="retryConnection()">
                重试连接
            </button>
            <a href="/" class="btn btn-secondary">
                返回首页
            </a>
        </div>

        <div class="network-status">
            <span class="status-indicator status-offline" id="statusIndicator"></span>
            <span id="statusText">网络连接已断开</span>
        </div>

        <div class="cache-info">
            <p>💾 正在显示缓存内容 | 🔄 网络恢复后将自动更新</p>
        </div>
    </div>

    <script>
        // 网络状态监控
        function updateNetworkStatus() {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                indicator.className = 'status-indicator status-online';
                statusText.textContent = '网络连接已恢复';
                
                // 网络恢复后自动跳转
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                indicator.className = 'status-indicator status-offline';
                statusText.textContent = '网络连接已断开';
            }
        }

        // 重试连接
        function retryConnection() {
            const btn = event.target;
            const originalText = btn.textContent;
            
            btn.textContent = '检查中...';
            btn.disabled = true;
            
            // 尝试发送一个简单的请求来检查网络
            fetch('/', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(() => {
                btn.textContent = '连接成功！';
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            })
            .catch(() => {
                btn.textContent = '仍然离线';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.disabled = false;
                }, 2000);
            });
        }

        // 监听网络状态变化
        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);

        // 初始化网络状态
        updateNetworkStatus();

        // 定期检查网络状态
        setInterval(() => {
            if (!navigator.onLine) {
                // 尝试发送请求检查实际网络状态
                fetch('/', { 
                    method: 'HEAD',
                    cache: 'no-cache',
                    signal: AbortSignal.timeout(5000)
                })
                .then(() => {
                    if (!navigator.onLine) {
                        // 手动触发online事件
                        window.dispatchEvent(new Event('online'));
                    }
                })
                .catch(() => {
                    // 网络确实不可用
                });
            }
        }, 10000); // 每10秒检查一次

        // 显示缓存信息
        if ('caches' in window) {
            caches.keys().then(cacheNames => {
                if (cacheNames.length > 0) {
                    const cacheInfo = document.querySelector('.cache-info p');
                    cacheInfo.innerHTML = `💾 已缓存 ${cacheNames.length} 个数据集 | 🔄 网络恢复后将自动更新`;
                }
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'r' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                retryConnection();
            }
            if (e.key === 'Escape') {
                window.location.href = '/';
            }
        });
    </script>
</body>
</html>
