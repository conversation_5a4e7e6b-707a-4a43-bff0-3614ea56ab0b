import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import { useFormik } from "formik";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";

import {
  addOutlet,
  editOutlet,
  getOutletDetail,
  getCustomOutletTypeList,
} from "@s/api/outlet";
import { toast } from "react-toastify";
import ZkFormik from "@c/Config/ZkFormik.jsx";
import { createValidation } from "@c/Config/validationUtils.js";
import { getFormConfig } from "./utils";
import CustomInput from "../Product/ProductInput";
import { getClientId, getClientCode } from "@/hooks/client.js";

function AddOutlet(props) {
  const { t } = useTranslation();

  const navigate = useNavigate();
  const treeSelectRef = React.useRef(null);
  const { state } = useLocation();
  const [loading, setLoading] = React.useState(false);
  const [data, setData] = useState([]);
  const [formConfig, setFormConfig] = useState([]);
  const [customePayload, setCustomePayload] = useState({});
  const [newLabels, setNewLabels] = useState([]);
  const clientId = getClientId();
  const clinetCode = getClientCode();
  if (clientId) {
    sessionStorage.setItem("CLIENT_ID", clientId);
    sessionStorage.setItem("CLIENT_CODE", clinetCode);
  }

  useEffect(() => {
    if (state?.type == "editor") {
      getOutletDetail(state?.id).then((res) => {
        setData(res?.data);
        treeSelectRef.current.setItem({
          id: res?.data?.areaId,
          name: res?.data?.areaName,
        });
        let params = {};

        res?.data?.outletTypeValueList?.forEach((item) => {
          params[item?.outletTypeId] = item.value;
        });

        setCustomePayload(params);
      });
    }

    getCustomOutletTypeList(sessionStorage.getItem("CLIENT_ID")).then((res) => {
      if (res?.data) {
        setNewLabels(res?.data);
      }
    });
  }, []);

  useEffect(() => {
    const payloadObject = {};
    newLabels.forEach((label) => {
      payloadObject[label.name] = "";
    });
    setCustomePayload(payloadObject);
  }, [newLabels]);

  useEffect(() => {
    const formConfig = getFormConfig(t, state?.type, treeSelectRef);
    setFormConfig(formConfig);
  }, []);

  const handleCustomChange = (name, value) => {
    setCustomePayload((prevPayload) => ({
      ...prevPayload,
      [name]: value,
    }));
  };

  // 添加表单
  let initialValues = {};

  if (state?.type == "editor") {
    initialValues = {
      id: state?.id,
      name: data?.name,
      type: 3,
      email: data?.email,
      countryCode: data?.countryCode,
      phone: data?.phone,
      // areaId: data?.areaId,
      areaName: data?.areaName,
      // longitude: data?.longitude,
      // latitude: data?.latitude,
      address: data?.address,
      timezone: data?.timezone,
      parentId: sessionStorage.getItem("CLIENT_ID"),
      clientCode: sessionStorage.getItem("CLIENT_CODE"),
    };
  } else {
    initialValues = {
      name: "",
      type: 3,
      email: "",
      countryCode: "",
      phone: "",
      // areaId: "",
      // longitude: "",
      // latitude: "",
      address: "",
      timezone: "",
      areaName: "",
      parentId: sessionStorage.getItem("CLIENT_ID"),
      clientCode: sessionStorage.getItem("CLIENT_CODE"),
    };
  }

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: createValidation(formConfig),
    enableReinitialize: true, // 允许重新初始化
    onSubmit: (values, { setErrors, setStatus, setSubmitting }) => {
      try {
        if (state?.type == "editor") {
          setLoading(true);

          let formData = new FormData();
          Object.keys(values).forEach((key) => {
            formData.append(key, values[key]);
          });

          formData.append("params", JSON.stringify(customePayload));
          editOutlet(formData).then((res) => {
            toast.success(res?.message);
            navigate("/retail/list");
          });
        } else {
          setLoading(true);
          let formData = new FormData();
          Object.keys(values).forEach((key) => {
            formData.append(key, values[key]);
          });

          formData.append("params", JSON.stringify(customePayload));

          addOutlet(formData).then((res) => {
            toast.success(res?.message);
            navigate("/retail/list");
          });
        }
      } finally {
        setLoading(false);
        setStatus({ success: false });
        setErrors({ submit: err.message });
        setSubmitting(false);
      }
    },
  });

  return (
    <RightViewLayout
      navigateBack={"/retail/list"}
      title={
        state?.type === "editor"
          ? t("outlets.edit_outlet_principal")
          : t("outlets.add_outlet_principal")
      }
      handleSubmit={formik.handleSubmit}
      handleCancle={() => {
        navigate("/retail/list");
      }}
      loading={loading}>
      <ZkFormik sx={6} formik={formik} formConfig={formConfig}></ZkFormik>

      <Grid container xs={12} md={12} item spacing={2} mb={1} mt={1}>
        {newLabels.map((field, index) => (
          <Grid key={index} item md={6} xs={6}>
            <CustomInput
              size="small"
              label={field.name}
              value={customePayload[field.id]}
              handleChange={(event) =>
                handleCustomChange(field.id, event.target.value)
              }
              name={field.name}
              inputProps={{
                maxLength: 50,
              }}
              resetError={() => console.log()}
              fullWidth
              placeholder={t("product.please_enter") + field.name}
            />
          </Grid>
        ))}
      </Grid>
    </RightViewLayout>
  );
}

export default AddOutlet;
