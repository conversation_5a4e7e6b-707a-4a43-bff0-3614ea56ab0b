import RegexCode from "@/enums/RegexCode";
import ZKSearchTree from "../../../ZKSearchTree.jsx";
export const getFormConfig = (t, type) => {
  let formConfig = [
    {
      name: "name",
      label: t("branch.name"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("branch.enter_name"),
        },
      ],
    },
    {
      name: "areaId",
      required: true,
      custom: true,
      label: t("branch.branch_region"),
      renderingCustomItem: (item, formik) => {
        return (
          <Grid xs={6} pl={3} mt={3}>
            <ZKSearchTree formik={formik} {...item} />
          </Grid>
        );
      },

      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("branch.branch_region_required"),
        },
      ],
    },

    {
      name: "email",
      label: t("branch.branch_email"),
      type: "input",
      required: true,
      disabled: type == "editor" ? true : false,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("branch.branch_email_required"),
        },
        {
          type: "email",
          message: t("subscription.email_format"),
        },
      ],
    },
    {
      codename: "countryCode", // 对应区号字段名
      name: "phone", // 对应电话号码字段名
      label: t("branch.branch_mobile"),
      type: "mobile",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common.common_enter_mobile"),
        },

        {
          type: "matches",
          matches: /^\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,4}$/,
          message: t("common.common_mobile_format"),
        },
      ],
    },

    {
      name: "password",
      label: t("common.common_password"),
      type: "password",
      viewPwd: true,
      required: type !== "editor" ? true : false,
      display: type !== "editor" ? true : false,
      validation:
        type !== "editor"
          ? [
              {
                type: "string",
                message: "",
              },
              {
                type: "required",
                message: t("common.common_required_password"),
              },
              {
                type: "matches",
                matches: RegexCode.PASSWORD_REGEX,
                message: t("common.common_format"),
              },
            ]
          : null,
    },

    {
      name: "confirmPassword",
      label: t("common.common_confirm_password"),
      type: "password",
      viewPwd: true,
      display: type !== "editor" ? true : false,
      required: type !== "editor" ? true : false,
      validation:
        type !== "editor"
          ? [
              {
                type: "string",
                message: "",
              },
              {
                type: "required",
                message: t("common.common_required_confirm_password"),
              },
              {
                type: "secondConfirm",
                ref: "password",
                message: t("common.common_confirm_password_not_match"),
              },
            ]
          : null,
    },

    {
      name: "address",
      label: t("ips.ips_store_address"),
      type: "address",
      required: true,
      placeholder: t("outlets.enter_address"),
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common.common_input_address"),
        },
      ],
    },
  ];

  return formConfig;
};
