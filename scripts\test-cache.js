#!/usr/bin/env node

/**
 * 缓存系统测试脚本
 * 用于验证缓存功能是否正常工作
 */

const puppeteer = require('puppeteer');
const path = require('path');

async function testCacheSystem() {
  console.log('🧪 开始测试缓存系统...\n');

  let browser;
  try {
    // 启动浏览器
    browser = await puppeteer.launch({
      headless: false, // 设置为 true 可以无头模式运行
      devtools: true,
      args: ['--disable-web-security', '--allow-running-insecure-content']
    });

    const page = await browser.newPage();
    
    // 启用请求拦截
    await page.setRequestInterception(true);
    
    const requests = [];
    const cachedRequests = [];
    
    page.on('request', request => {
      requests.push({
        url: request.url(),
        method: request.method(),
        resourceType: request.resourceType()
      });
      request.continue();
    });

    page.on('response', response => {
      const cacheHeader = response.headers()['cache-control'];
      if (cacheHeader) {
        cachedRequests.push({
          url: response.url(),
          status: response.status(),
          cacheControl: cacheHeader,
          fromCache: response.fromCache()
        });
      }
    });

    // 测试1: 访问主页
    console.log('📄 测试1: 访问主页...');
    await page.goto('http://localhost:8080', { waitUntil: 'networkidle2' });
    
    // 检查Service Worker注册
    const swRegistered = await page.evaluate(() => {
      return 'serviceWorker' in navigator;
    });
    
    console.log(`   Service Worker 支持: ${swRegistered ? '✅' : '❌'}`);

    if (swRegistered) {
      const swRegistration = await page.evaluate(async () => {
        const registration = await navigator.serviceWorker.getRegistration();
        return registration ? {
          scope: registration.scope,
          state: registration.active?.state
        } : null;
      });
      
      console.log(`   Service Worker 状态: ${swRegistration ? swRegistration.state : '未注册'}`);
    }

    // 测试2: 检查缓存API
    console.log('\n💾 测试2: 检查缓存API...');
    const cacheSupported = await page.evaluate(() => {
      return 'caches' in window;
    });
    
    console.log(`   Cache API 支持: ${cacheSupported ? '✅' : '❌'}`);

    if (cacheSupported) {
      const cacheNames = await page.evaluate(async () => {
        return await caches.keys();
      });
      
      console.log(`   缓存数量: ${cacheNames.length}`);
      cacheNames.forEach(name => {
        console.log(`     - ${name}`);
      });
    }

    // 测试3: 检查缓存命中
    console.log('\n🎯 测试3: 测试缓存命中...');
    
    // 第一次加载
    await page.reload({ waitUntil: 'networkidle2' });
    const firstLoadRequests = requests.length;
    
    // 第二次加载
    await page.reload({ waitUntil: 'networkidle2' });
    const secondLoadRequests = requests.length - firstLoadRequests;
    
    console.log(`   第一次加载请求数: ${firstLoadRequests}`);
    console.log(`   第二次加载请求数: ${secondLoadRequests}`);
    
    const cacheHitRate = secondLoadRequests < firstLoadRequests ? 
      ((firstLoadRequests - secondLoadRequests) / firstLoadRequests * 100).toFixed(2) : 0;
    
    console.log(`   缓存命中率: ${cacheHitRate}%`);

    // 测试4: 离线功能
    console.log('\n🔌 测试4: 测试离线功能...');
    
    // 模拟离线
    await page.setOfflineMode(true);
    
    try {
      await page.reload({ waitUntil: 'networkidle2', timeout: 10000 });
      console.log('   离线模式下页面加载: ✅');
    } catch (error) {
      console.log('   离线模式下页面加载: ❌');
      console.log(`   错误: ${error.message}`);
    }
    
    // 恢复在线
    await page.setOfflineMode(false);

    // 测试5: 缓存管理器功能
    console.log('\n⚙️ 测试5: 测试缓存管理器...');
    
    const cacheManagerTest = await page.evaluate(async () => {
      try {
        // 检查缓存管理器是否可用
        if (typeof window.cacheManager === 'undefined') {
          return { error: '缓存管理器未找到' };
        }
        
        // 获取缓存信息
        const cacheInfo = await window.cacheManager.getCacheInfo();
        
        return {
          success: true,
          cacheCount: Object.keys(cacheInfo).length,
          totalEntries: Object.values(cacheInfo).reduce((sum, cache) => sum + cache.count, 0)
        };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    if (cacheManagerTest.success) {
      console.log(`   缓存管理器: ✅`);
      console.log(`   缓存数量: ${cacheManagerTest.cacheCount}`);
      console.log(`   总条目数: ${cacheManagerTest.totalEntries}`);
    } else {
      console.log(`   缓存管理器: ❌ ${cacheManagerTest.error}`);
    }

    // 测试6: PWA功能
    console.log('\n📱 测试6: 测试PWA功能...');
    
    const manifestTest = await page.evaluate(async () => {
      try {
        const response = await fetch('/manifest.json');
        const manifest = await response.json();
        return {
          success: true,
          name: manifest.name,
          hasIcons: manifest.icons && manifest.icons.length > 0
        };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    if (manifestTest.success) {
      console.log(`   Manifest 文件: ✅`);
      console.log(`   应用名称: ${manifestTest.name}`);
      console.log(`   图标配置: ${manifestTest.hasIcons ? '✅' : '❌'}`);
    } else {
      console.log(`   Manifest 文件: ❌ ${manifestTest.error}`);
    }

    // 生成测试报告
    console.log('\n📊 测试报告:');
    console.log('=====================================');
    console.log(`Service Worker: ${swRegistered ? '✅ 支持' : '❌ 不支持'}`);
    console.log(`Cache API: ${cacheSupported ? '✅ 支持' : '❌ 不支持'}`);
    console.log(`缓存命中率: ${cacheHitRate}%`);
    console.log(`缓存管理器: ${cacheManagerTest.success ? '✅ 正常' : '❌ 异常'}`);
    console.log(`PWA配置: ${manifestTest.success ? '✅ 正常' : '❌ 异常'}`);
    console.log('=====================================');

    // 评分
    const scores = [
      swRegistered ? 20 : 0,
      cacheSupported ? 20 : 0,
      cacheHitRate > 50 ? 20 : cacheHitRate > 20 ? 10 : 0,
      cacheManagerTest.success ? 20 : 0,
      manifestTest.success ? 20 : 0
    ];
    
    const totalScore = scores.reduce((sum, score) => sum + score, 0);
    
    console.log(`\n🏆 总体评分: ${totalScore}/100`);
    
    if (totalScore >= 80) {
      console.log('🎉 缓存系统运行良好！');
    } else if (totalScore >= 60) {
      console.log('⚠️ 缓存系统基本正常，但有改进空间');
    } else {
      console.log('❌ 缓存系统存在问题，需要检查配置');
    }

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// 检查是否安装了puppeteer
function checkPuppeteer() {
  try {
    require('puppeteer');
    return true;
  } catch (error) {
    console.error('❌ 未找到 puppeteer，请先安装:');
    console.error('   npm install --save-dev puppeteer');
    return false;
  }
}

// 主函数
async function main() {
  if (!checkPuppeteer()) {
    process.exit(1);
  }
  
  console.log('🚀 缓存系统测试工具');
  console.log('确保应用正在 http://localhost:8080 运行\n');
  
  await testCacheSystem();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testCacheSystem };
