import React, { useEffect, useState } from "react";
import C<PERSON><PERSON><PERSON> from "@/assets/Images/Logo/CmsLogo.png";
import NugtagLogo from "@/assets/Images/Logo/NutagLogo.png";
import Zata<PERSON>ogo from "@/assets/Images/Logo/zataLogo.png";
import QueueIcon from "@/assets/Icons/Queue.svg?react";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import { styled } from "@mui/material/styles";
import { useNavigate } from "react-router-dom";
import { getAllPackage } from "@s/api/subscription.js";
import { useTranslation } from "react-i18next";
import { cn } from "@/utils/cn";
import { useStateUserInfo } from "@/hooks/user";

function Index(props) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const userInfo = useStateUserInfo();

  const [selectApp, setSelectApp] = useState(
    sessionStorage.getItem("SUB_APP") || "SD"
  );
  const [packageList, setPackageList] = useState([]);

  useEffect(() => {
    getAllPackage({ applicationCode: selectApp }).then((res) => {
      // 给每个对象增加不同的 description 和 confidence 字段
      const updatedPackageList = res?.data.map((item) => {
        if (item.id == "1" || item.id == "41" || item.id == "31") {
          item.appManageContent = t("2 Devices for 15 Days");
        } else if (item.id == "2" || item.id == "42" || item.id == "32") {
          item.appManageContent = t(
            "per Device/Month (billed Annually) <br /> US$11 billed Monthly"
          );
        } else if (item.id == "3" || item.id == "33") {
          item.appManageContent = t(
            "per Device/Month (billed Annually) <br /> US$11 billed Monthly"
          );
        } else if (item.id == "4") {
          item.appManageContent = t(
            "per Device/Month (billed Annually) <br /> US$11 billed Monthly"
          );
        }

        return item;
      });

      setPackageList(updatedPackageList);
      sessionStorage.setItem("SUB_TYPE", JSON.stringify(updatedPackageList));
    });
  }, [selectApp]);

  return (
    <React.Fragment>
      <RightViewLayout
        title={t("subscription.subscription")}
        isShowSaveButton={false}>
        <div className="w-full h-full flex gap-5 flex-col items-center py-5 px-5">
          <Grid container className="w-full flex gap-10 items-center ">
            <ApplicationTag
              imageUrl={CMSLogo}
              Text={t("subscription.cmsText")}
              isSelected={selectApp == "SD"}
              onClick={() => {
                sessionStorage.setItem("SUB_APP", "SD");
                setSelectApp("SD");
              }}></ApplicationTag>

            <ApplicationTag
              imageUrl={NugtagLogo}
              Text={t("subscription.nutagText")}
              // sx={{
              //   ml: 10,
              //   height: "100px",
              // }}
              isSelected={selectApp == "NT"}
              onClick={() => {
                sessionStorage.setItem("SUB_APP", "NT");
                setSelectApp("NT");
              }}></ApplicationTag>

            <ApplicationTag
              imageUrl={ZataLogo}
              Text={t("subscription.zataText")}
              // sx={{
              //   ml: 10,
              // }}
              isSelected={selectApp == "ZT"}
              onClick={() => {
                sessionStorage.setItem("SUB_APP", "ZT");
                setSelectApp("ZT");
              }}></ApplicationTag>
          </Grid>
          <Divider />
          <div className="flex flex-row gap-6  items-start flex-wrap w-full h-full ">
            {packageList?.map((item, index) => {
              return (
                <div key={index}>
                  <SubscriptionContent
                    dataList={item?.showResources}
                    // packageName={t(`subscription.${item.packageI18n}`)}
                    packageName={item?.packageName}
                    indate={item?.price}
                    title={item?.appManageContent}
                    disabled={true}
                    onClick={() => {
                      navigate("/add/scription", {
                        state: {
                          type: "add",
                          id: item.id,
                          deviceCount: item.deviceCount,
                          isFree: item.isFree,
                        },
                      });
                    }}
                    isEnabled={
                      item?.isEnabled !== 0 ||
                      userInfo.roles[0] == "PrincipalAdmin"
                    }></SubscriptionContent>
                </div>
              );
            })}
          </div>
        </div>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default Index;

const ApplicationTag = (props) => {
  const { sx, style, imageUrl, Text, isSelected = false, onClick } = props;
  return (
    <div
      className={cn(
        "flex-row items-center flex gap-4 relative   cursor-pointer",
        isSelected &&
          "after:content-[''] after:absolute after:bottom-[-20px] after:w-full after:h-[5px] after:bg-[#78BC27]"
      )}
      onClick={onClick}>
      <img
        src={imageUrl}
        style={{
          ...style,
          width: 40,
        }}
        alt="加载失败"></img>
      <div
        className="text-nowrap text-xl  font-bold"
        dangerouslySetInnerHTML={{ __html: Text }}></div>
    </div>
  );
};

const SubscriptionContent = (props) => {
  const { t } = useTranslation();
  const { dataList, onClick, packageName, title, indate, isEnabled } = props;

  const ButtonStyled = styled(Button)`
    width: 285px;
    height: 60px;
    font: normal normal bold 16px/19px Proxima Nova;
    color: #474b4f;
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid #e0e0e0;
    cursor: pointer;
    transition: background 0.3s ease;

    &:hover {
      background: linear-gradient(to right, #78bc27, #1487cb) !important;
      color: #fff !important;
    }
  `;

  return (
    <Grid
      container
      sx={{
        width: "325px",
        flexGrow: 1,
        border: "1px solid #78BC27",
        borderRadius: "15px",
      }}>
      <Grid
        item
        sx={{
          width: "355px",
          height: "150px",
          background: `linear-gradient(to right, #FFFFFF, #EDEDEF)`,
          borderRadius: "15px 15px 0px 0px",
          backgroundSize: "100% 100%",
          border: "1px solid #E0E0E0",
          boxShadow: `0px 0px 10px #0000000D`,
        }}>
        <Grid>
          <Typography
            sx={{
              font: `normal normal bold 16px/20px Proxima Nova`,
              ml: 3,
              mt: 2,
            }}>
            {packageName}
          </Typography>

          <Typography
            sx={{
              font: `normal normal bold 28px/34px Roboto;`,
              mt: 1,
              ml: 3,
            }}>
            {indate == "0" ? t("Free") : `US${indate} $`}
          </Typography>

          <Typography
            sx={{
              font: `normal normal normal 14px/16px Proxima Nova`,
              ml: 3,
              mt: 2,
              lineHeight: "25px",
            }}>
            <span dangerouslySetInnerHTML={{ __html: title }}></span>
          </Typography>
        </Grid>
      </Grid>

      <InputLabel
        style={{
          color: "#000",
          fontSize: "18px",
          fontWeight: 700,
          marginTop: "12px",
          marginLeft: "12px",
        }}>
        {t("subscription.includes")}
      </InputLabel>

      <Grid
        item
        sx={{
          width: "325px",
          maxHeight: "310px",
          overflowY: "auto",
        }}>
        {dataList?.map((item, index) => {
          return (
            <Grid
              item
              index={index}
              sx={{
                display: "flex",
                alignItems: "center",
                mt: 2,
                ml: 2,
              }}>
              <div
                style={{
                  width: "8px",
                  height: "8px",
                  background: "#78bc27",
                  borderRadius: "50%",
                  display: "inline-block", // 确保元素是块级显示
                  minWidth: "8px", // 添加最小宽度
                  minHeight: "8px", // 添加最小高度
                  aspectRatio: "1 / 1", // 强制宽高比为1:1
                  flexShrink: 0, // 防止在flex布局中被压缩
                }}></div>
              <Box
                sx={{
                  font: `normal normal normal 14px/16px Roboto`,
                  color: "#474B4F",
                  ml: 2,
                }}>
                <span
                  style={{
                    lineHeight: "25px",
                  }}
                  dangerouslySetInnerHTML={{ __html: item }}></span>
              </Box>
            </Grid>
          );
        })}
      </Grid>

      <Grid item ml={4} mt={2} mb={2}>
        <ButtonStyled
          style={{
            width: "285px",
            height: "60px",
            font: `normal normal bold 16px/19px Proxima Nova;`,
            borderRadius: "10px",
            border: isEnabled
              ? "1px solid rgba(156, 143, 143, 0.47)"
              : "1px solid #E0E0E0",
            color: "#474B4F",
            background: isEnabled ? "#ccc" : "#fff",
          }}
          onClick={onClick}
          disabled={isEnabled}>
          {t("subscription.get_started")}
        </ButtonStyled>
      </Grid>
    </Grid>
  );
};
