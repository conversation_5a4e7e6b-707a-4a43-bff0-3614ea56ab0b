import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  Chip,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  CircularProgress,
} from "@mui/material";
import {
  Storage as StorageIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  CloudDownload as CloudDownloadIcon,
  Info as InfoIcon,
} from "@mui/icons-material";
import cacheManager from "@/utils/cacheManager";

/**
 * 缓存状态显示组件
 * 用于开发和调试时查看缓存状态
 */
const CacheStatus = ({ open, onClose }) => {
  const [cacheStats, setCacheStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 加载缓存统计信息
  const loadCacheStats = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const stats = await cacheManager.getCacheStats();
      setCacheStats(stats);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // 清理指定缓存
  const handleClearCache = async (cacheName) => {
    if (window.confirm(`确定要清理缓存 "${cacheName}" 吗？`)) {
      try {
        await cacheManager.clearCache(cacheName);
        await loadCacheStats(); // 重新加载统计信息
      } catch (err) {
        setError(`清理缓存失败: ${err.message}`);
      }
    }
  };

  // 清理所有缓存
  const handleClearAllCaches = async () => {
    if (window.confirm('确定要清理所有缓存吗？这将影响应用性能。')) {
      try {
        await cacheManager.clearAllCaches();
        await loadCacheStats();
      } catch (err) {
        setError(`清理所有缓存失败: ${err.message}`);
      }
    }
  };

  // 导出缓存配置
  const handleExportConfig = async () => {
    try {
      const config = await cacheManager.exportCacheConfig();
      const blob = new Blob([config], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `cache-config-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      setError(`导出配置失败: ${err.message}`);
    }
  };

  useEffect(() => {
    if (open) {
      loadCacheStats();
    }
  }, [open]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: '600px' }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <StorageIcon color="primary" />
          <Typography variant="h6">缓存状态管理</Typography>
          <IconButton onClick={loadCacheStats} disabled={loading}>
            <RefreshIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        {loading && (
          <Box display="flex" justifyContent="center" p={3}>
            <CircularProgress />
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {cacheStats && (
          <Box>
            {/* 总体统计 */}
            <Card sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  缓存总览
                </Typography>
                <Box display="flex" gap={2} flexWrap="wrap">
                  <Chip
                    icon={<StorageIcon />}
                    label={`${cacheStats.totalCaches} 个缓存`}
                    color="primary"
                  />
                  <Chip
                    icon={<InfoIcon />}
                    label={`${cacheStats.totalEntries} 个条目`}
                    color="secondary"
                  />
                  <Chip
                    icon={<CloudDownloadIcon />}
                    label={`总大小: ${cacheStats.totalSize}`}
                    color="success"
                  />
                </Box>
              </CardContent>
            </Card>

            {/* 详细缓存信息 */}
            <Typography variant="h6" gutterBottom>
              缓存详情
            </Typography>

            {Object.entries(cacheStats.caches).map(([cacheName, cacheInfo]) => (
              <Accordion key={cacheName} sx={{ mb: 1 }}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Box display="flex" alignItems="center" gap={2} width="100%">
                    <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
                      {cacheName}
                    </Typography>
                    <Chip
                      size="small"
                      label={`${cacheInfo.count} 条目`}
                      color="primary"
                    />
                    <Chip
                      size="small"
                      label={cacheManager.formatCacheSize(cacheInfo.size)}
                      color="secondary"
                    />
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleClearCache(cacheName);
                      }}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <List dense>
                    {cacheInfo.urls.slice(0, 10).map((url, index) => (
                      <ListItem key={index}>
                        <ListItemText
                          primary={url}
                          primaryTypographyProps={{
                            variant: 'body2',
                            sx: { 
                              wordBreak: 'break-all',
                              fontSize: '0.75rem'
                            }
                          }}
                        />
                      </ListItem>
                    ))}
                    {cacheInfo.urls.length > 10 && (
                      <ListItem>
                        <ListItemText
                          primary={`... 还有 ${cacheInfo.urls.length - 10} 个条目`}
                          primaryTypographyProps={{
                            variant: 'body2',
                            color: 'text.secondary',
                            fontStyle: 'italic'
                          }}
                        />
                      </ListItem>
                    )}
                  </List>
                </AccordionDetails>
              </Accordion>
            ))}

            {Object.keys(cacheStats.caches).length === 0 && (
              <Alert severity="info">
                当前没有缓存数据
              </Alert>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleExportConfig} startIcon={<CloudDownloadIcon />}>
          导出配置
        </Button>
        <Button 
          onClick={handleClearAllCaches} 
          color="error"
          startIcon={<DeleteIcon />}
        >
          清理所有缓存
        </Button>
        <Button onClick={onClose}>
          关闭
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CacheStatus;
