//新增设备数量模块配置信息
import RegexCode from "@/enums/RegexCode";

export const getDeviceNumberConfig = (t, state, type, dataList) => {
  //订阅类型  1升级变更 2增加设备 3续订（增加时间）

  let baseConfig = [
    {
      name: "packageId",
      type: "Radio",
      sx: 12,
      options: dataList.map((item) => {
        return {
          value: item.id,
          label: t(`subscription.${item.packageI18n}`),
          disabledData:
            state?.type == "add" ||
            item?.isFree === 0 ||
            item?.isEnabled === 1 ||
            state?.type === "2" ||
            state?.type === "3",
        };
      }),
    },
    {
      name: "deviceCount",
      label: t("subscription.numberOfDevice"),
      type: "input",
      placeholder: t("subscription.enter_Dev_number"),
      required: true,
      disabled:
        (state?.type === "add" && state?.isFree == 0) || state?.type === "3",
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("subscription.enter_Dev_number"),
        },
        {
          type: "number",
          message: t("subscription.only_number"),
        },
      ],
    },

    {
      name: "unit",
      type: "Radio",
      sx: 12,
      conditionalRendering: () => {
        // 当 state.type 为 "add" 时，检查是否收费
        const isAddAndSpecialId = state?.type === "add" && state?.isFree === 1;

        // 返回 true 表示显示，false 表示隐藏
        return isAddAndSpecialId || state?.type === "3" || state?.type === "1";
      },
      options: [
        {
          value: "0",
          label: t("subscription.Monthly"),
        },
        {
          value: "1",
          label: t("subscription.Annually"),
        },
      ],
    },

    {
      name: "count",
      label:
        state?.type == "3"
          ? type == "0"
            ? t("subscription.extend_months")
            : t("subscription.extend_years")
          : type == "0"
            ? t("subscription.subMonths")
            : t("subscription.subAnnually"),
      type: "input",
      conditionalRendering: () => {
        return (
          (state?.type === "add" && state?.isFree == 1) ||
          state?.type === "3" ||
          state?.type === "1"
        );
      },

      required:
        (state?.type == "add" && state?.isFree == 1) ||
        state?.type == "3" ||
        state?.type == "1",
      placeholder:
        type == "0"
          ? t("subscription.please_enter_number_months")
          : t("subscription.please_enter_number_Annually"),
      validation:
        (state?.type == "add" && state?.isFree == 1) ||
          state?.type == "3" ||
          state?.type == "1"
          ? [
            {
              type: "string",
              message: "",
            },
            {
              type: "required",
              message: t("subscription.enter_years_time"),
            },
            {
              type: "number",
              message: t("subscription.enter_Dev_number"),
            },
          ]
          : null,
    },

    {
      name: "activateTime",
      label: t("subscription.activeDate"),
      type: "date",
      placeholder: t("subscription.selectActiveDate"),
      required: true,
      disabled: state?.type === "2",
      size: "small",
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("subscription.please_select_startTime"),
        },
      ],
    },

    {
      name: "expiraTime",
      label: t("subscription.expirationDate"),
      type: "date",
      placeholder: t("subscription.selectExpirationDate"),
      required: true,
      disabled: true,
      size: "small",
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("subscription.please_select_endTime"),
        },
      ],
    },
  ];

  return baseConfig;
};

export const getAccountConfig = (t, state, isSelected, setOpen, formik) => {
  let formConfig = [
    {
      name: "departmentName",
      label: t("principal.principal_name"),
      placeholder: t("principal.enter_principal_name"),
      type: "input",
      required: true,
      conditionalRendering: () => {
        return state?.type == "add" && isSelected == "0";
      },
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("principal.principal_name_required"),
        },
      ],
    },
    {
      name: "contactEmail",
      label: t("principal.principal_owner_email"),
      type: "input",
      required: true,
      disabled: state?.type !== "add",
      placeholder: t("principal.principal_owner_email_required"),
      onClick: () => {
        if (isSelected == "1") {
          setOpen(true);
        }
      },
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("principal.principal_owner_email_required"),
        },
        {
          type: "email",
          message: t("subscription.email_formatted"),
        },
      ],
    },

    {
      name: "password",
      label: t("common.common_password"),
      type: "password",
      viewPwd: true,
      required: true,
      conditionalRendering: () => {
        return state?.type == "add" && isSelected == "0";
      },
      validation:
        state?.type !== "editor"
          ? [
            {
              type: "string",
              message: "",
            },
            {
              type: "required",
              message: t("common.common_required_password"),
            },
            {
              type: "matches",
              matches: RegexCode.PASSWORD_REGEX,
              message: t("common.common_format"),
            },
          ]
          : null,
    },

    {
      codename: "countryCode", // 对应区号字段名
      name: "contactPhone", // 对应电话号码字段名
      label: t("common.common_mobile"),
      type: "mobile",
      required: true,
      conditionalRendering: () => {
        return state?.type == "add" && isSelected == "0";
      },
      placeholder: t("common.common_country_code"),
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common.common_required_mobile"),
        },
        {
          type: "matches",
          matches: /^\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,4}$/,
          message: t("common.common_mobile_format"),
        },
      ],
    },

    {
      name: "areaName",
      label: t("subscription.principal_region"),
      type: "input",
      required: true,
      placeholder: t("subscription.enter_region_name"),
      conditionalRendering: () => {
        return state?.type == "add" && isSelected == "0";
      },
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("principal.area_required"),
        },
      ],
    },

    {
      name: "address",
      label: t("branch_user.address"),
      type: "address",
      required: true,
      placeholder: t("common.common_input_address"),
      conditionalRendering: () => {
        return state?.type == "add" && isSelected == "0";
      },
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common.common_input_address"),
        },
      ],
    },
  ];

  return formConfig;
};

export const getContactConfig = (t, state, unitList) => {
  let baseConfig = [
    {
      name: "contractNo",
      label: t("common.common_comtract_id"),
      placeholder: t("common.common_comtract_id_enter"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common.common_comtract_id_not_null"),
        },
      ],
    },
    {
      name: "contractAmount",
      label: t("common.common_comtract_amount"),
      type: "input",
      required: true,
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common.common_required_comtract_amount"),
        },
        {
          type: "number",
          message: t("subscription.only_number"),
        },
      ],
    },

    {
      name: "contractAmountUnit",
      label: t("common.common_contract_unit"),
      type: "select",
      required: true,
      typevalue: "3",
      options: unitList || [],
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("common.common_required_contract_unit"),
        },
      ],
    },
  ];

  return baseConfig;
};
