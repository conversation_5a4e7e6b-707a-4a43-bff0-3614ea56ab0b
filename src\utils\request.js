import axios from "axios";
import { getToken, removeToken } from "@/utils/auth";
import { tansParams } from "@/utils/zkUtils";
import cache from "./sessionCache";
import { RespCode } from "@/enums/RespCode";
import ErrorCode from "@/enums/ErrorCode";
import i18n from "i18next";

// import { getLanguage } from '@/lang';
import { getStoreLang, converLang } from "@/utils/langUtils";
import {
  encryptBase64,
  encryptWithAes,
  generateAesKey,
  decryptWithAes,
  decryptBase64,
} from "@/utils/crypto";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import { toast } from "react-toastify";

const encryptHeader = "X-API-KEY";
// 是否显示重新登录
export const isRelogin = { show: false };
export const globalHeaders = () => {
  return {
    Authorization: import.meta.env.VITE_TOKEN_HEADER + getToken(),
  };
};

axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8";
// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 50000,
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 对应国际化资源文件后缀
    config.headers["Accept-Language"] = converLang(getStoreLang());

    const isToken = config.headers?.isToken === false;
    // 是否需要防止数据重复提交
    const isRepeatSubmit = config.headers?.repeatSubmit === false;
    // 是否需要加密
    const isEncrypt = config.headers?.isEncrypt === "true";

    if (getToken() && !isToken) {
      config.headers["Authorization"] =
        import.meta.env.VITE_TOKEN_HEADER + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
    }

    let selectTenantId = sessionStorage.getItem("tenantCode");
    if (selectTenantId) {
      config.headers["Tenant-Code"] = selectTenantId;
    } else {
      let loginTenantId = sessionStorage.getItem("tenantCode");
      if (loginTenantId) {
        config.headers["Tenant-Code"] = loginTenantId;
      }
    }

    // get请求映射params参数
    if (config.method === "get" && config.params) {
      let url = config.url + "?" + tansParams(config.params);
      url = url.slice(0, -1);
      config.params = {};
      config.url = url;
    }

    if (
      !isRepeatSubmit &&
      (config.method === "post" || config.method === "put")
    ) {
      const requestObj = {
        url: config.url,
        data:
          typeof config.data === "object"
            ? JSON.stringify(config.data)
            : config.data,
        time: new Date().getTime(),
      };
      const sessionObj = cache.session.getJSON("sessionObj");
      if (
        sessionObj === undefined ||
        sessionObj === null ||
        sessionObj === ""
      ) {
        cache.session.setJSON("sessionObj", requestObj);
      } else {
        const s_url = sessionObj.url; // 请求地址
        const s_data = sessionObj.data; // 请求数据
        const s_time = sessionObj.time; // 请求时间
        const interval = 500; // 间隔时间(ms)，小于此时间视为重复提交
        if (
          s_data === requestObj.data &&
          requestObj.time - s_time < interval &&
          s_url === requestObj.url
        ) {
          const message = i18n.t("errorCode.data_repeat")
          console.warn(`[${s_url}]: ` + message);
          return Promise.reject(new Error(message));
        } else {
          cache.session.setJSON("sessionObj", requestObj);
        }
      }
    }
    if (import.meta.env.VITE_APP_ENCRYPT === "true") {
      // 当开启参数加密
      if (isEncrypt && (config.method === "post" || config.method === "put")) {
        // 生成一个 AES 密钥
        const aesKey = generateAesKey();
        config.headers[encryptHeader] = encrypt(encryptBase64(aesKey));
        config.data =
          typeof config.data === "object"
            ? encryptWithAes(JSON.stringify(config.data), aesKey)
            : encryptWithAes(config.data, aesKey);
      }
    }
    // FormData数据去请求头Content-Type
    if (config.data instanceof FormData) {
      delete config.headers["Content-Type"];
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (res) => {
    // 判断是否需要 token 处理
    const isToken = res.config.headers?.isToken !== false;
    // 如果不需要 token，直接返回响应数据
    if (!isToken) {
      return Promise.resolve(res.data);
    }

    if (import.meta.env.VITE_APP_ENCRYPT === "true") {
      // 加密后的 AES 秘钥
      const keyStr = res.headers[encryptHeader];
      // 加密
      if (keyStr != null && keyStr != "") {
        const data = res.data;
        // 请求体 AES 解密
        const base64Str = decrypt(keyStr);
        // base64 解码 得到请求头的 AES 秘钥
        const aesKey = decryptBase64(base64Str.toString());
        // aesKey 解码 data
        const decryptData = decryptWithAes(data, aesKey);
        // 将结果 (得到的是 JSON 字符串) 转为 JSON
        res.data = JSON.parse(decryptData);
      }
    }

    // 未设置状态码则默认成功状态
    const code = res.data.code || RespCode.SUCCESS;
    // 获取错误信息
    const msg = ErrorCode[code] || res.data.message || ErrorCode["default"];

    // 二进制数据则直接返回
    if (
      res.request.responseType === "blob" ||
      res.request.responseType === "arraybuffer"
    ) {
      return res.data;
    }

    if (code === RespCode.SUCCESS) {
      return Promise.resolve(res.data);
    }

    if (
      code === RespCode.LOGIN_EXPIREDIRED ||
      code === RespCode.ERROR_EXPIREDIRED
    ) {
      if (!isRelogin.show) {
        isRelogin.show = true;
        toast.warn(i18n.t(`errorCode.${RespCode.LOGIN_EXPIREDIRED}`));
        removeToken();
        window.location.href = "/login";
      }
      removeToken();
      return Promise.reject(i18n.t(`errorCode.${RespCode.LOGIN_EXPIREDIRED}`));
    } else {
      toast.error(msg);
      // 不再全局处理错误，而是将错误传递给调用方
      return Promise.reject(res.data);
    }
  },
  (error) => {
    let { message, response } = error;
    const status = response?.status;

    // 根据HTTP状态码处理不同的错误
    if (status >= 500 && status < 600) {
      // 服务器错误 (500-599) - 跳转到500页面
      console.error('服务器错误，跳转到500页面:', error);
      // window.location.href = '/500';
      return Promise.reject(error);
    } else if (status === 403) {
      // 权限错误 - 跳转到403页面
      console.error('权限错误，跳转到403页面:', error);
      window.location.href = '/403';
      return Promise.reject(error);
    } else if (status === 404) {
      // 接口不存在 - 跳转到404页面
      console.error('接口不存在，跳转到404页面:', error);
      window.location.href = '/404';
      return Promise.reject(error);
    }

    // 处理网络错误和超时错误（不再跳转页面）
    if (message == "Network Error") {
      message = i18n.t("errorCode.networkError");
      // 网络错误 - 只记录日志，不跳转页面
      console.error('网络错误:', error);
    } else if (message.includes("timeout")) {
      message = i18n.t("errorCode.timeout");
      // 超时错误 - 只记录日志，不跳转页面
      console.error('请求超时:', error);
    } else if (message.includes("Request failed with status code")) {
      message = i18n.t("errorCode.error");
    }

    // 其他错误显示toast提示，不跳转页面
    toast.error(message);
    return Promise.reject(error);
  }
);
// 导出 axios 实例
export default service;
