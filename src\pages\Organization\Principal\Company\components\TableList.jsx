import { useDispatchClient } from "@/hooks/client.js";
import ZktecoTable from "@c/ZktecoTable";
import React from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useConfirm } from "@/components/zkconfirm";
import { deletePrincipa } from "@s/api/principal";
import { toast } from "react-toastify";
function TableList({
  data,
  isLoading,
  isRefetching,
  isError,
  rowCount,
  pagination,
  setPagination,
  setOpen,
  setUserId,
  getTableData,
  switchDepartment,
}) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const confirmFn = useConfirm();
  const dispatch = useDispatch();
  const { stateSetClientId, stateSetClientCode } = useDispatchClient();

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "logo",
        header: t("common.common_company_logo"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
        Cell: ({ row }) => (
          <Avatar
            src={row.original.photo}
            alt={"Logo"}
            size="medium"
            sx={{
              width: "121px",
              height: "50px",
              borderRadius: "8px",
              border: "1px solid #E3E3E3",
            }}
          />
        ),
      },
      {
        accessorKey: "name",
        header: t("principal.principal_name"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "email",
        header: t("branch_user.email"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      {
        accessorKey: "phone",
        header: t("branch_user.phone"),
        enableColumnActions: false,
        enableClickToCopy: false, // 启用点击复制
        enableSorting: false,
      },
      // {
      //   accessorKey: "address",
      //   header: t("area.title"),
      //   enableColumnActions: false,
      //   enableClickToCopy: false, // 启用点击复制
      //   enableSorting: false,
      // },
    ],
    []
  );

  const isShowAction = {
    isShowView: "org:principal:query",
    isShowEditor: "org:principal:update",
    isShowUserSetting: true,
    isShowDetele: "org:principal:delete",
    isShowUnion: true,
    isShowSendEamil: false,
    isShowHandover: "org:login:switch",
  };

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  // 删除
  const handlerDetele = async (data) => {
    confirmFn({
      title: t("common.common_delete_confirm"),
      confirmationText: t("common.common_confirm"),
      cancellationText: t("table.cancel"),
      description: t("branch.delete_content"),
    }).then(() => {
      deletePrincipa(data?.id).then((res) => {
        toast.success(res.message);
        getTableData();
      });
    });
  };

  const actionHandlers = useMemo(
    () => ({
      handlerView: (data) =>
        navigate("/view/principal", { state: { id: data?.id, type: "view" } }),
      handlerEditor: (data) =>
        navigate("/add/principal", {
          state: { id: data?.id, type: "editor" },
        }),
      handlerUserSetting: (data) => {
        dispatch(stateSetClientId(data?.id));
        dispatch(stateSetClientCode(data?.code));
        navigate("/principal/employee/list", {
          state: { id: data?.id },
        });
      },
      Detele: (data) => {
        handlerDetele(data);
      },
      handlerUnion: (data) => {
        dispatch(stateSetClientId(data?.id));
        dispatch(stateSetClientCode(data?.code));
        navigate("/retail/list", {
          state: { id: data?.id },
        });
      },
      handlerHandover: (data) => {
        switchDepartment(data.id);
      },
    }),
    [
      dispatch,
      navigate,
      setOpen,
      setUserId,
      stateSetClientId,
      stateSetClientCode,
    ]
  );

  return (
    <React.Fragment>
      <ZktecoTable
        columns={columns}
        data={data}
        rowCount={rowCount}
        isLoading={isLoading}
        isRefetching={isRefetching}
        isError={isError}
        pathRoute="/add/principal"
        loadDada={getTableData}
        paginationProps={{
          currentPage: pagination.pageIndex,
          rowsPerPage: pagination.pageSize,
          onPageChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
        }}
        actionHandlers={actionHandlers}
        isShowAction={isShowAction}
      />
    </React.Fragment>
  );
}

export default TableList;
