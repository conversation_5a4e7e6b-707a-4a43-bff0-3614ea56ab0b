import { useEffect, useState, useCallback } from "react";
import { registerMicroApps, start } from "qiankun";
import { ToastContainer } from "react-toastify";

// 样式和国际化
import "@/lang/index";
import "@/styles/global.less";

// 组件导入
import RouterWaiter from "./components/routerWaiter";
import ThemeCustomization from "./themes";
import { ConfirmProvider } from "./components/zkconfirm";
import ScrollTop from "@c/ScrollTop";
import ErrorBoundary from "./router/ExceptionComponent/ErrorBoundary";
import CacheDevTools from "@/components/DevTools/CacheDevTools";

// 路由和工具
import routes from "./router/routers";
import onRouteBefore from "./router/onRouteBefore";
import { initGlobalErrorHandler } from "./utils/globalErrorHandler";

// 微前端配置导入
import {
  createMicroAppConfig,
  QIANKUN_CONFIG,
  TOAST_CONFIG,
  checkEnvironmentConfig,
} from "./config/microAppConfig";

const App = () => {
  // 注册微前端应用
  const registerMicroAppsWithConfig = useCallback(() => {
    try {
      if (!checkEnvironmentConfig()) {
        return;
      }

      const microAppsConfig = createMicroAppConfig();
      registerMicroApps(microAppsConfig);

      // 启动qiankun
      start(QIANKUN_CONFIG);
    } catch (error) {
      console.error("微前端应用注册失败:", error);
      // 即使注册失败也不跳转到500页面，让主应用继续运行
    }
  }, []);

  useEffect(() => {
    // 初始化全局错误处理器
    initGlobalErrorHandler();

    // 注册微前端应用
    registerMicroAppsWithConfig();
  }, [registerMicroAppsWithConfig]);

  return (
    <ErrorBoundary>
      <ThemeCustomization>
        <ConfirmProvider>
          <ScrollTop>
            <RouterWaiter routes={routes} onRouteBefore={onRouteBefore} />
            <ToastContainer {...TOAST_CONFIG} />
            {/* <CacheDevTools /> */}
          </ScrollTop>
        </ConfirmProvider>
      </ThemeCustomization>
    </ErrorBoundary>
  );
};

export default App;
