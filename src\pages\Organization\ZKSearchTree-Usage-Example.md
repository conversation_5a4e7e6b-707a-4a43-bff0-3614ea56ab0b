# ZKSearchTree 组件使用指南

## 概述

ZKSearchTree 组件已经改造为支持 formik 表单管理，可以自动保存选中的区域 ID 到 formik 的表单状态中。

## 主要特性

1. **Formik 集成**：自动与 formik 表单状态同步
2. **向后兼容**：仍然支持 localStorage 存储（当没有 formik 时）
3. **表单验证**：支持显示 formik 验证错误信息
4. **双向绑定**：formik 值变化时组件状态自动更新

## Props 接口

```javascript
{
  // 基础属性
  onChange: Function,           // 选择变化回调
  placeholder: String,          // 占位符文本
  label: String,               // 标签文本
  labelpostion: String,        // 标签位置 "left" | "top"
  required: Boolean,           // 是否必填
  
  // Formik 集成
  formik: Object,              // formik 实例
  name: String,                // formik 字段名
  
  // 向后兼容
  regionKey: String,           // localStorage 存储键名
  onClear: Function,           // 清除回调
  isContainOldData: String,    // 是否包含旧数据
}
```

## 使用方式

### 1. 在表单配置中使用

```javascript
// utils.jsx
import ZKSearchTree from "../../../ZKSearchTree.jsx";

export const getFormConfig = (t, type) => {
  return [
    {
      name: "areaId",
      required: true,
      custom: true,
      label: t("branch.branch_region"),
      renderingCustomItem: (item, formik) => {
        return (
          <Grid xs={6} pl={3} mt={3}>
            <ZKSearchTree 
              formik={formik} 
              name="areaId"
              label={t("branch.branch_region")}
              required={true}
              placeholder={t("branch.select_region")}
              onChange={(selectedArea) => {
                console.log("选中的区域:", selectedArea);
              }}
            />
          </Grid>
        );
      },
      validation: [
        {
          type: "string",
          message: "",
        },
        {
          type: "required",
          message: t("branch.branch_region_required"),
        },
      ],
    },
  ];
};
```

### 2. 在组件中直接使用

```javascript
import { useFormik } from "formik";
import ZKSearchTree from "./ZKSearchTree";

const MyComponent = () => {
  const formik = useFormik({
    initialValues: {
      areaId: "", // 区域 ID
    },
    onSubmit: (values) => {
      console.log("表单值:", values);
      // values.areaId 将包含选中的区域信息
      // 格式: { id: "123", name: "北京市" }
    },
  });

  return (
    <form onSubmit={formik.handleSubmit}>
      <ZKSearchTree
        formik={formik}
        name="areaId"
        label="选择区域"
        required={true}
        placeholder="请选择区域"
        onChange={(selectedArea) => {
          console.log("选中的区域:", selectedArea);
        }}
      />
      <button type="submit">提交</button>
    </form>
  );
};
```

## 数据格式

### formik 中存储的数据格式

```javascript
{
  areaId: {
    id: "123",      // 区域 ID
    name: "北京市"   // 区域名称
  }
}
```

### onChange 回调返回的数据格式

```javascript
{
  id: "123",
  name: "北京市",
  location: { lat: 39.9042, lng: 116.4074 }, // 可选
  // ...其他节点属性
}
```

## 验证配置

在表单配置中添加验证规则：

```javascript
validation: [
  {
    type: "string",
    message: "",
  },
  {
    type: "required",
    message: "请选择区域",
  },
  {
    type: "test",
    callback: (value, ctx) => {
      if (value && typeof value === 'object' && value.id) {
        return true;
      }
      return ctx.createError({ message: "请选择有效的区域" });
    },
  },
]
```

## 注意事项

1. **字段名一致性**：确保 `name` 属性与 formik 的字段名一致
2. **数据格式**：组件会将选中的区域保存为对象格式 `{ id, name }`
3. **向后兼容**：如果不传递 `formik` 和 `name`，组件会回退到使用 `regionKey` 和 localStorage
4. **验证错误**：组件会自动显示 formik 的验证错误信息

## 迁移指南

### 从旧版本迁移

如果你之前使用的是基于 localStorage 的版本：

```javascript
// 旧版本
<ZKSearchTree
  regionKey="selectedArea"
  onChange={handleChange}
/>

// 新版本（推荐）
<ZKSearchTree
  formik={formik}
  name="areaId"
  onChange={handleChange}
/>

// 或者保持兼容（仍然使用 localStorage）
<ZKSearchTree
  regionKey="selectedArea"
  onChange={handleChange}
/>
```
