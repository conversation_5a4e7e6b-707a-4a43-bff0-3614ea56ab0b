import React, { useState, forwardRef, useEffect } from "react";
import { Stack, InputLabel, FormHelperText } from "@mui/material";
import SearchTree from "@/components/AntdTreeSelect/index";
import RequirePoint from "@c/RequirePoint";
import { treeList, subTreeList } from "@/service/api/area";
import i18n from "i18next";
const ZKSearchTree = forwardRef((props, ref) => {
  const {
    onChange,
    label,
    placeholder = i18n.t("common.common_enter") + " " + `${label}`,
    isContainOldData = "0",
    regionKey,
    onClear,
    labelpostion,
    formik = null,
    name, // formik 字段名
    required = false,
  } = props;
  const [treeData, setTreeData] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [loadingKeys, setLoadingKeys] = useState([]);

  // 获取当前值的函数
  const getCurrentValue = () => {
    if (formik && name) {
      // 优先使用 formik 的值
      const formikValue = formik.values[name];

      // 尝试从对应的 name 字段获取名称
      const nameField = name.replace("Id", "Name"); // areaId -> areaName
      const formikName = formik.values[nameField] || "";

      if (formikValue) {
        if (typeof formikValue === "object") {
          return {
            id: formikValue.id || formikValue.value || "",
            name: formikValue.name || formikValue.label || formikName,
          };
        } else {
          return { id: formikValue, name: formikName };
        }
      }
      return { id: "", name: "" };
    } else if (regionKey) {
      // 回退到 localStorage
      try {
        const stored = localStorage.getItem(regionKey);
        return stored ? JSON.parse(stored) : { id: "", name: "" };
      } catch (error) {
        return { id: "", name: "" };
      }
    }
    return { id: "", name: "" };
  };

  const currentValue = getCurrentValue();
  const [valueId, setValueId] = useState(currentValue.id);
  const [valueName, setValueName] = useState(currentValue.name);

  // 监听 formik 值的变化，保持组件状态同步
  useEffect(() => {
    if (formik && name) {
      const formikValue = formik.values[name];
      const nameField = name.replace("Id", "Name"); // areaId -> areaName
      const formikName = formik.values[nameField] || "";

      if (formikValue) {
        if (typeof formikValue === "object") {
          const id = formikValue.id || formikValue.value || "";
          const itemName = formikValue.name || formikValue.label || formikName;
          setValueId(id);
          setValueName(itemName);
        } else {
          setValueId(formikValue);
          setValueName(formikName);
        }
      } else {
        setValueId("");
        setValueName("");
      }
    }
  }, [formik?.values, name]); // 监听整个 formik.values 对象的变化

  // 保存值到 localStorage
  const saveToStorage = (id, name) => {
    if (!regionKey) return;
    try {
      const valueToStore = { id, name };
      localStorage.setItem(regionKey, JSON.stringify(valueToStore));
    } catch (error) {
      console.error("保存到 localStorage 失败:", error);
    }
  };

  // 清除 localStorage 中的值
  const clearStorage = () => {
    if (!regionKey) return;
    try {
      localStorage.removeItem(regionKey);
    } catch (error) {
      console.error("清除 localStorage 失败:", error);
    }
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  // 当树数据加载完成且有持久化值时，触发 onChange 回调
  useEffect(() => {
    if (treeData.length > 0 && valueId && valueName) {
      // 通知父组件恢复的值
      onChange?.({
        id: valueId,
        name: valueName,
      });
    }
  }, [treeData, valueId, valueName]);

  const loadInitialData = () => {
    treeList({ isContainOldData: isContainOldData }).then((res) => {
      if (res?.code == "00000000") {
        // 为初始数据设置 isLeaf 属性，假设所有一级节点都可能有子节点
        const processedData = res.data.map((node) => ({
          ...node,
          isLeaf: false, // 一级节点默认不是叶子节点，显示折叠图标
          subRows: node.subRows || [], // 确保有 subRows 属性
        }));
        setTreeData(processedData);
      } else {
        setTreeData([]);
      }
    });
  };

  // 真正的数据加载函数，只在展开操作时调用
  const loadDataForExpand = async (node) => {
    console.log("� 展开操作 - 开始加载数据 - 节点:", node.name);

    // 防止重复加载
    if (loadingKeys.includes(node.id)) {
      console.log("❌ 跳过加载 - 正在加载中");
      return Promise.resolve();
    }

    // 检查节点是否已经有子数据，如果有则不需要重新加载
    if (node.subRows && node.subRows.length > 0) {
      console.log("❌ 跳过加载 - 节点已有子数据，数量:", node.subRows.length);
      return Promise.resolve();
    }

    setLoadingKeys((prev) => [...prev, node.id]);

    return new Promise((resolve) => {
      subTreeList(node.id)
        .then((res) => {
          if (res?.code == "00000000") {
            setTreeData((prevTreeData) =>
              updateTreeData(prevTreeData, node.id, res.data, false)
            );
            console.log(
              "✅ 数据加载成功 - 节点:",
              node.name,
              "子数据数量:",
              res.data?.length || 0
            );
          } else {
            setTreeData((prevTreeData) =>
              updateTreeData(prevTreeData, node.id, [], false)
            );
            console.log("⚠️ 数据加载失败 - 节点:", node.name);
          }
        })
        .catch((error) => {
          setTreeData((prevTreeData) =>
            updateTreeData(prevTreeData, node.id, [], false)
          );
          console.error("❌ 数据加载异常 - 节点:", node.name, error);
        })
        .finally(() => {
          setLoadingKeys((prev) => prev.filter((key) => key !== node.id));
          resolve();
        });
    });
  };

  const updateTreeData = (list, key, children, forceLeaf = true) => {
    return list.map((node) => {
      if (node.id === key) {
        // 为新加载的子节点设置 isLeaf 属性
        const processedChildren = children.map((child) => ({
          ...child,
          isLeaf: false, // 假设子节点也可能有子节点，显示折叠图标
          subRows: child.subRows || [],
        }));

        return {
          ...node,
          subRows: processedChildren,
          isLeaf: forceLeaf ? processedChildren.length === 0 : false,
        };
      }
      if (node.subRows) {
        return {
          ...node,
          subRows: updateTreeData(node.subRows, key, children, forceLeaf),
        };
      }
      return node;
    });
  };

  // 清除选中的值
  const clearItem = () => {
    setValueId("");
    setValueName("");

    // 清除 formik 值
    if (formik && name) {
      formik.setFieldValue(name, "");
      // 同时清除对应的名称字段
      const nameField = name.replace("Id", "Name");
      formik.setFieldValue(nameField, "");
    }

    // 清除 localStorage（如果有 regionKey）
    if (regionKey) {
      clearStorage();
    }

    onClear?.(); // 调用外部传入的清除回调
  };

  React.useImperativeHandle(ref, () => ({
    setItem,
    clearItem,
  }));

  const setItem = (item) => {
    const id = item?.id || "";
    const itemName = item?.name || "";

    setValueId(id);
    setValueName(itemName);

    // 更新 formik 值
    if (formik && name) {
      formik.setFieldValue(name, id);
      // 同时更新对应的名称字段
      const nameField = name.replace("Id", "Name");
      formik.setFieldValue(nameField, itemName);
    }

    // 保存到 localStorage（如果有 regionKey）
    if (regionKey) {
      if (id && itemName) {
        saveToStorage(id, itemName);
      } else {
        clearStorage();
      }
    }
  };

  return (
    <Stack spacing={1}>
      <Stack
        direction={labelpostion === "left" ? "row" : "column"}
        sx={{
          alignItems: labelpostion === "left" ? "flex-start" : "",
        }}
        spacing={1}>
        {label && (
          <InputLabel
            style={{
              marginTop: labelpostion === "left" ? "12px" : "",
              color: "#474b4fcc",
              fontSize: "14px",
            }}
            htmlFor={"zkInput_" + name}>
            {label} {required && <RequirePoint></RequirePoint>}
          </InputLabel>
        )}

        <Stack
          sx={{
            flexGrow: 1,
            width: "100%",
          }}>
          <SearchTree
            treeData={treeData}
            fieldNames={{
              label: "name",
              value: "id",
              children: "subRows",
            }}
            virtual={true}
            showSearch={true}
            allowClear={!!(valueId && valueName)} // 只有当有值时才显示清除图标
            treeLine={true}
            treeDefaultExpandAll={false}
            size="medium"
            virtualThreshold={100}
            virtualItemSize={40}
            maxTagCount={3}
            // 恢复 loadData 属性，但通过内部标志控制是否真正执行加载
            loadData={loadDataForExpand}
            // 尝试不同的value格式
            value={{ value: valueId, label: valueName }} // 使用对象格式
            labelInValue={true}
            placeholder={placeholder}
            onTreeExpand={(newExpandedKeys, info) => {
              // 更新展开状态
              setExpandedKeys(newExpandedKeys);
              const node = info.node;
              loadDataForExpand(node);
            }}
            treeExpandedKeys={expandedKeys}
            onChange={(valObj, node) => {
              if (valObj?.value && valObj?.label) {
                const id = valObj.value;
                const itemName = valObj.label;

                setValueId(id);
                setValueName(itemName);

                // 更新 formik 值
                if (formik && name) {
                  formik.setFieldValue(name, id);
                  // 同时更新对应的名称字段
                  const nameField = name.replace("Id", "Name");
                  formik.setFieldValue(nameField, itemName);
                }

                // 保存到 localStorage（如果有 regionKey）
                if (regionKey) {
                  saveToStorage(id, itemName);
                }

                onChange?.({
                  id,
                  name: itemName,
                  location: valObj?.location,
                  ...node,
                });
              } else {
                setValueId("");
                setValueName("");

                // 清除 formik 值
                if (formik && name) {
                  formik.setFieldValue(name, "");
                  // 同时清除对应的名称字段
                  const nameField = name.replace("Id", "Name");
                  formik.setFieldValue(nameField, "");
                }

                // 清除 localStorage（如果有 regionKey）
                if (regionKey) {
                  clearStorage();
                }

                onChange?.(undefined);
              }
            }}
            onSelect={(selectedValue, node) => {
              if (selectedValue && node) {
                const id = selectedValue;
                const itemName = node.name;

                setValueId(id);
                setValueName(itemName);

                // 更新 formik 值
                if (formik && name) {
                  formik.setFieldValue(name, id);
                  // 同时更新对应的名称字段
                  const nameField = name.replace("Id", "Name");
                  formik.setFieldValue(nameField, itemName);
                }

                // 保存到 localStorage（如果有 regionKey）
                if (regionKey) {
                  saveToStorage(id, itemName);
                }

                // 手动关闭下拉框
                setTimeout(() => {
                  const event = new MouseEvent("click", {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                  });
                  document.body.dispatchEvent(event);
                }, 100);

                onChange?.({
                  id,
                  name: itemName,
                  ...node,
                });
              }
            }}
            // 确保选择后关闭下拉框
            dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
            // 添加更多属性尝试解决问题
            showArrow={true}
            treeNodeFilterProp="name"
            treeNodeLabelProp="name"
          />

          {/* 显示 formik 验证错误 */}
          {formik && name && formik.touched[name] && formik.errors[name] && (
            <FormHelperText error sx={{ mt: 1 }}>
              {formik.errors[name]}
            </FormHelperText>
          )}
        </Stack>
      </Stack>
    </Stack>
  );
});

export default ZKSearchTree;
