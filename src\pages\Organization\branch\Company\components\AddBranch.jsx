import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import RightViewLayout from "@c/layoutComponent/RightViewLayout";
import { useNavigate, useLocation } from "react-router-dom";
import { useFormik } from "formik";
import { savetenant, updatetenant, getTenantDetail } from "@s/api/tenant";
import { toast } from "react-toastify";
import ZkFormik from "@c/Config/ZkFormik.jsx";
import { createValidation } from "@c/Config/validationUtils.js";
import { getFormConfig } from "./utils";

function AddBranch(props) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const treeSelectRef = React.useRef(null);
  const { state } = useLocation();
  const [loading, setLoading] = React.useState(false);
  const [detailData, setDetailData] = useState([]);

  const formConfig = getFormConfig(t, state?.type, treeSelectRef);
  useEffect(() => {
    if (state?.type === "editor" || state?.type === "view") {
      getTenantDetail(state?.id).then((res) => {
        setDetailData(res?.data);
        treeSelectRef.current.setItem({
          id: res?.data?.areaId,
          name: res?.data?.areaName,
        });
      });
    }
  }, []);

  let initialValues = {};
  if (state?.type !== "editor") {
    initialValues = {
      name: "",
      areaId: "",
      email: "",
      countryCode: "",
      phone: "",
      // password: "",
      // confirmPassword: "",
      address: "",
    };
  } else {
    initialValues = {
      id: detailData?.id,
      name: detailData?.name,
      areaId: detailData?.areaId,
      areaName: detailData?.areaName,
      email: detailData?.email,
      countryCode: detailData?.countryCode,
      phone: detailData?.phone,
      address: detailData?.address,
    };
  }

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: createValidation(formConfig),
    enableReinitialize: true, // 允许重新初始化
    onSubmit: (values) => {
      if (!values.countryCode) {
        toast.error(t("Please Select Country Code"));

        return;
      }

      if (state?.type === "editor") {
        setLoading(true);

        updatetenant(values)
          .then((res) => {
            toast.success(res?.message);
            navigate("/branch");
          })
          .finally(() => {
            setLoading(false);
          });
      } else {
        setLoading(true);
        savetenant(values)
          .then((res) => {
            toast.success(res?.message);
            navigate("/branch");
          })
          .finally(() => {
            setLoading(false);
          });
      }
    },
  });

  return (
    <React.Fragment>
      <RightViewLayout
        navigateBack={"/branch"}
        title={
          state?.type === "editor"
            ? t("branch.edit_branch")
            : t("branch.add_branch")
        }
        handleSubmit={formik.handleSubmit}
        handleCancle={() => {
          navigate("/branch");
        }}
        loading={loading}>
        <ZkFormik sx={6} formik={formik} formConfig={formConfig}></ZkFormik>
      </RightViewLayout>
    </React.Fragment>
  );
}

export default AddBranch;
