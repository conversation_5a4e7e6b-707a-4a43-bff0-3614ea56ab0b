import React, { useState } from "react";
import {
  Fab,
  Badge,
  Tooltip,
  Box,
  Typography,
  Card,
  CardContent,
  Chip,
  IconButton,
  Collapse,
} from "@mui/material";
import {
  Storage as StorageIcon,
  Speed as SpeedIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from "@mui/icons-material";
import { styled } from "@mui/material/styles";
import CacheStatus from "@/components/CacheStatus";
import { useCachePerformance } from "@/hooks/useCachePerformance";

// 悬浮工具栏样式
const FloatingToolbar = styled(Box)(({ theme }) => ({
  position: "fixed",
  bottom: theme.spacing(2),
  right: theme.spacing(2),
  zIndex: 9999,
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(1),
}));

// 性能指标卡片样式
const MetricsCard = styled(Card)(({ theme }) => ({
  position: "fixed",
  bottom: theme.spacing(10),
  right: theme.spacing(2),
  width: "300px",
  zIndex: 9998,
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.shadows[8],
}));

/**
 * 缓存开发者工具
 * 仅在开发环境下显示，提供缓存监控和管理功能
 */
const CacheDevTools = () => {
  const [showCacheDialog, setShowCacheDialog] = useState(false);
  const [showMetrics, setShowMetrics] = useState(false);
  const [metricsExpanded, setMetricsExpanded] = useState(true);

  const {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    resetMetrics,
    getCacheRecommendations,
  } = useCachePerformance();

  // 只在开发环境下显示
  if (process.env.NODE_ENV !== "development") {
    return null;
  }

  const recommendations = getCacheRecommendations();
  const hasWarnings = recommendations.some(r => r.type === 'warning');

  const formatNumber = (num) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  };

  const getHitRateColor = (rate) => {
    if (rate >= 80) return 'success';
    if (rate >= 60) return 'warning';
    return 'error';
  };

  return (
    <>
      <FloatingToolbar>
        {/* 缓存状态按钮 */}
        <Tooltip title="缓存管理" placement="left">
          <Fab
            size="small"
            color="primary"
            onClick={() => setShowCacheDialog(true)}
          >
            <StorageIcon />
          </Fab>
        </Tooltip>

        {/* 性能监控按钮 */}
        <Tooltip title="性能监控" placement="left">
          <Badge
            badgeContent={hasWarnings ? "!" : null}
            color="error"
          >
            <Fab
              size="small"
              color={isMonitoring ? "secondary" : "default"}
              onClick={() => {
                if (isMonitoring) {
                  setShowMetrics(!showMetrics);
                } else {
                  startMonitoring();
                  setShowMetrics(true);
                }
              }}
            >
              <SpeedIcon />
            </Fab>
          </Badge>
        </Tooltip>
      </FloatingToolbar>

      {/* 性能指标卡片 */}
      {showMetrics && isMonitoring && (
        <MetricsCard>
          <CardContent sx={{ pb: 1 }}>
            <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
              <Typography variant="subtitle2" fontWeight="bold">
                缓存性能监控
              </Typography>
              <Box>
                <IconButton
                  size="small"
                  onClick={() => setMetricsExpanded(!metricsExpanded)}
                >
                  {metricsExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => setShowMetrics(false)}
                >
                  <CloseIcon />
                </IconButton>
              </Box>
            </Box>

            <Collapse in={metricsExpanded}>
              <Box display="flex" flexDirection="column" gap={1}>
                {/* 基础指标 */}
                <Box display="flex" gap={1} flexWrap="wrap">
                  <Chip
                    size="small"
                    label={`命中率: ${metrics.cacheHitRate}%`}
                    color={getHitRateColor(metrics.cacheHitRate)}
                  />
                  <Chip
                    size="small"
                    label={`请求: ${formatNumber(metrics.totalRequests)}`}
                    color="primary"
                  />
                </Box>

                <Box display="flex" gap={1} flexWrap="wrap">
                  <Chip
                    size="small"
                    label={`命中: ${formatNumber(metrics.cacheHits)}`}
                    color="success"
                  />
                  <Chip
                    size="small"
                    label={`未命中: ${formatNumber(metrics.cacheMisses)}`}
                    color="error"
                  />
                </Box>

                <Box display="flex" gap={1} flexWrap="wrap">
                  <Chip
                    size="small"
                    label={`平均耗时: ${metrics.averageLoadTime}ms`}
                    color={metrics.averageLoadTime > 1000 ? "warning" : "default"}
                  />
                </Box>

                <Box display="flex" gap={1} flexWrap="wrap">
                  <Chip
                    size="small"
                    label={`缓存大小: ${metrics.cacheSize}`}
                    color="info"
                  />
                  <Chip
                    size="small"
                    label={metrics.isOnline ? "在线" : "离线"}
                    color={metrics.isOnline ? "success" : "error"}
                  />
                </Box>

                {/* 建议 */}
                {recommendations.length > 0 && (
                  <Box mt={1}>
                    <Typography variant="caption" color="text.secondary">
                      优化建议:
                    </Typography>
                    {recommendations.slice(0, 2).map((rec, index) => (
                      <Typography
                        key={index}
                        variant="caption"
                        display="block"
                        color={rec.type === 'warning' ? 'error' : 'text.secondary'}
                        sx={{ fontSize: '0.7rem' }}
                      >
                        • {rec.title}
                      </Typography>
                    ))}
                  </Box>
                )}

                {/* 控制按钮 */}
                <Box display="flex" gap={1} mt={1}>
                  <Chip
                    size="small"
                    label="重置"
                    onClick={resetMetrics}
                    clickable
                    variant="outlined"
                  />
                  <Chip
                    size="small"
                    label="停止"
                    onClick={() => {
                      stopMonitoring();
                      setShowMetrics(false);
                    }}
                    clickable
                    variant="outlined"
                    color="error"
                  />
                </Box>
              </Box>
            </Collapse>
          </CardContent>
        </MetricsCard>
      )}

      {/* 缓存状态对话框 */}
      <CacheStatus
        open={showCacheDialog}
        onClose={() => setShowCacheDialog(false)}
      />
    </>
  );
};

export default CacheDevTools;
