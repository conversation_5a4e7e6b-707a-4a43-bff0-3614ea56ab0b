import { loginlogList } from "@s/loginlog";
import { useFormik } from "formik";
import SearchForm from "@c/SearchForm";
import ZktecoTable from "@c/ZktecoTable";
import { pxToRem } from "@u/zkUtils";
import React from "react";
import { Grid, Typography } from "@mui/material";
import LayoutList from "@l/components/LayoutList";
import CustomInput from "@c/CustInput.jsx";
import CustomSelect from "@c/CustomSelect";
import dayjs from "dayjs";
import ZkTooltip from "@c/ZkTooltip";
function LoginLogList(props) {
  const { t } = useTranslation();
  const [isError, setIsError] = useState(false);
  // 表格加载
  const [isLoading, setIsLoading] = useState(false);
  // 表格数据
  const [data, setData] = useState([]);
  // 重新去发请求
  const [isRefetching, setIsRefetching] = useState(false);
  // 总数
  const [rowCount, setRowCount] = useState(0);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 5,
  });
  // 构建参数
  const buildParams = () => {
    const params = {
      page: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
    };

    return params;
  };

  // 获取数据
  const getTableData = async () => {
    if (!data.length) {
      setIsLoading(true);
    } else {
      setIsRefetching(true);
    }

    try {
      await loginlogList(buildParams()).then((res) => {
        setData(res?.data?.data);
        setRowCount(res?.data?.total);
      });
    } finally {
      setIsLoading(false);
      setIsRefetching(false);
    }
  };
  useEffect(() => {
    getTableData();
  }, [pagination.pageIndex, pagination.pageSize]);

  // 列字段
  const columns = useMemo(
    () => [
      {
        accessorKey: "userName", //access nested data with dot notation
        header: t("login_log.account"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: (e) => {
          return (
            <ZkTooltip title={e.row.original.userName} arrow placement="bottom">
              <span>{e.row.original.userName}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "realName", //access nested data with dot notation
        header: t("login_log.user_name"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: (e) => {
          return (
            <ZkTooltip title={e.row.original.realName} arrow placement="bottom">
              <span>{e.row.original.realName}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "ipAddr", //access nested data with dot notation
        header: t("login_log.login_ip"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <ZkTooltip title={row.original.ipAddr} arrow placement="bottom">
              <span>{row.original.ipAddr}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "location", //access nested data with dot notation
        header: t("login_log.login_location"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <ZkTooltip title={row.original.location} arrow placement="bottom">
              <span>{row.original.location}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "accessTime", //access nested data with dot notation
        header: t("login_log.login_time"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <ZkTooltip
              title={dayjs(row.original.accessTime).format(
                "YYYY-MM-DD HH:mm:ss"
              )}
              arrow
              placement="bottom">
              <span>
                {dayjs(row.original.accessTime).format("YYYY-MM-DD HH:mm:ss")}
              </span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "msg", //access nested data with dot notation
        header: t("login_log.login_message"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ row }) => {
          return (
            <ZkTooltip title={row.original.msg} arrow placement="bottom">
              <span>{row.original.msg}</span>
            </ZkTooltip>
          );
        },
      },
      {
        accessorKey: "status", //access nested data with dot notation
        header: t("login_log.status"),
        enableColumnActions: false,
        enableSorting: false,
        Cell: ({ row }) => {
          return row.original.status == 0 ? (
            <Typography color={"#78BC27"}>
              {t("common.common_success")}
            </Typography>
          ) : (
            <Typography color={"red"}>{t("common.common_failed")}</Typography>
          ); // 根据值显示对应的文本
        },
      },
    ],
    []
  );

  // 查询表单
  const queryFormik = useFormik({
    initialValues: {
      ...buildParams(),
      userName: "",
      realName: "",
      location: "",
      status: "",
    },
    onSubmit: async (values, { setErrors, setStatus, setSubmitting }) => {
      loginlogList(values)
        .then((res) => {
          if (res.code == "00000000") {
            setData(res?.data?.data);
            setRowCount(res?.data?.total);
          } else {
            setData([]);
            setRowCount(0);
          }
          setIsLoading(false);
          setIsRefetching(false);
        })
        .catch((err) => {
          setIsError(true);
          setIsLoading(false);
          setIsRefetching(false);
        });
    },
  });

  const loginStatus = [
    {
      id: "0",
      value: "成功",
    },
    {
      id: "1",
      value: "失败",
    },
  ];

  const handlePageChange = useCallback(
    (pageIndex) => {
      setPagination((prev) => ({ ...prev, pageIndex }));
    },
    [setPagination]
  );

  const handlePageSizeChange = useCallback(
    (pageSize) => {
      setPagination({ pageIndex: 0, pageSize });
    },
    [setPagination]
  );

  return (
    <React.Fragment>
      <LayoutList
        title={t("login_log.title")}
        header={
          <SearchForm formik={queryFormik} reloadData={getTableData}>
            <Grid container>
              <Grid item ml={2}>
                <CustomInput
                  value={queryFormik.values.userName}
                  onChange={queryFormik.handleChange}
                  onBlur={queryFormik.handleBlur}
                  size="small"
                  type="text"
                  width={pxToRem(264)}
                  name="userName"
                  fullWidth
                  placeholder={t("login_log.account")}
                />
              </Grid>

              <Grid item ml={2}>
                <CustomInput
                  value={queryFormik.values.realName}
                  onChange={queryFormik.handleChange}
                  onBlur={queryFormik.handleBlur}
                  size="small"
                  type="text"
                  name="realName"
                  width={pxToRem(264)}
                  fullWidth
                  placeholder={t("login_log.user_name")}
                />
              </Grid>

              <Grid item ml={2}>
                <CustomInput
                  value={queryFormik.values.location}
                  onChange={queryFormik.handleChange}
                  onBlur={queryFormik.handleBlur}
                  size="small"
                  type="text"
                  name="location"
                  width={pxToRem(264)}
                  fullWidth
                  placeholder={t("login_log.login_location")}
                />
              </Grid>

              <Grid item ml={2}>
                <CustomSelect
                  name="status"
                  items={loginStatus}
                  formik={queryFormik}
                  placeholder={t("login_log.status")}></CustomSelect>
              </Grid>
            </Grid>
          </SearchForm>
        }
        content={
          <ZktecoTable
            columns={columns}
            data={data}
            rowCount={rowCount}
            isLoading={isLoading}
            isRefetching={isRefetching}
            isError={isError}
            enableRowActions={false}
            loadDada={getTableData}
            paginationProps={{
              currentPage: pagination.pageIndex,
              rowsPerPage: pagination.pageSize,
              onPageChange: handlePageChange,
              onPageSizeChange: handlePageSizeChange,
            }}
            topActions={{
              showAdd: false,
            }}
            isShowAction={false}
          />
        }></LayoutList>
    </React.Fragment>
  );
}

export default LoginLogList;
