const common = {
  zoontime: {
    International: "(GMT-12:00) 国际日期变更线西",
    Coordinated: "(UTC-11) 协调世界时-11",
    Hawaii: "(UTC-10) 夏威夷",
    Alaska: "(UTC-9) 阿拉斯加",
    California: "(UTC-8) 太平洋时间（美国和加拿大）巴哈加利福尼亚",
    Arizona: "(UTC-7) 拉巴斯，山地时间（美国和加拿大），亚利桑那",
    America: "(UTC-6) 萨斯喀彻温省，中部时间，中美洲",
    Eastern:
      "(UTC-5) 波哥大，利马，基多，里约布兰科，东部时间，印第安纳州（东部）",
    Caracas: "(UTC-4:30) 加拉加斯",
    Atlantic: "(UTC-4) 大西洋时间，库亚巴，乔治敦，拉巴斯，圣地亚哥",
    Newfoundland: "(UTC-3:30) 纽芬兰",
    Brasilia: "(UTC-3) 巴西利亚，布宜诺斯艾利斯，格陵兰，卡宴",
    International2: "(UTC-2) 国际日期变更线西-02",
    Azores: "(UTC-1) 佛得角群岛，亚速尔群岛",
    Edinburgh: "(UTC) 都柏林，爱丁堡，里斯本，伦敦，国际日期变更线西",
    Brussels: "(UTC+1) 阿姆斯特丹，布鲁塞尔，萨拉热窝",
    Damascus: "(UTC+2) 贝鲁特，大马士革，东欧，开罗，雅典，耶路撒冷",
    Kuwait: "(UTC+3) 巴格达，科威特，莫斯科，圣彼得堡，内罗毕",
    Tehran: "(UTC+3:30) 德黑兰",
    Yerevan: "(UTC+4) 阿布扎比，埃里温，巴库，路易港，萨马拉",
    Kabul: "(UTC+4:30) 喀布尔",
    Karachi: "(UTC+5) 阿什哈巴德，伊斯兰堡，卡拉奇",
    Calcutta: "(UTC+5:30) 钦奈，加尔各答，孟买，新德里",
    Kathmandu: "(UTC+5:45) 加德满都",
    Novosibirsk: "(UTC+6) 阿斯塔纳，达卡，新西伯利亚",
    Yangon: "(UTC+6:30) 仰光",
    Jakarta: "(UTC+7) 曼谷，河内，雅加达",
    Beijing: "(UTC+08:00) 北京，重庆，香港，乌鲁木齐，吉隆坡，新加坡",
    Yakutsk: "(UTC+9) 大阪，东京，首尔，雅库茨克",
    Adelaide: "(UTC+9:30) 阿德莱德，达尔文",
    Canberra: "(UTC+10) 布里斯班，符拉迪沃斯托克，关岛，堪培拉",
    Islands: "(UTC+11) 所罗门群岛，新喀里多尼亚",
    Oakland: "(UTC+12) 阿纳德尔，奥克兰，惠灵顿，斐济",
    alofa: "(UTC+13) 努库阿洛法，萨摩亚群岛",
    Island: "(UTC+14) 圣诞岛",
  },

  common: {
    common_close_account: "注销账户",
    common_drag_over: "拖动滑块完成拼图",
    common_edit_ok: "确定",
    common_edit_cancel: "取消",
    common_delete: "删除",
    common_save: "保存",
    common_confirm: "确认",
    common_confirm_delete: "您确定要删除吗？",
    common_confirm_delete_tip: "此操作无法撤销",
    common_relatedOp: "操作",
    common_loading_error: "加载数据出错",
    common_rule_area_len60: "区域名称长度不能超过60个字符",
    common_area_name_regTip:
      "区域名称只能以字母、汉字开头，且只能包含字母、数字、汉字、空格和下划线",
    common_area_name_not_null: "区域不能为空",
    common_area_center_location: "区域中心点位置",
    common_please_area_center_location: "请选择区域中心点位置",
    common_input_sort_null: "排序不能为空",
    common_connot_lower_zero: "不能低于0",
    common_latitude: "纬度",
    common_submit: "提交",
    common_Longitude: "经度",
    common_online: "在线",
    common_offline: "离线",
    common_op_return: "返回",
    common_search_name: "请输入名称",
    common_delete_confirm: "删除确认",
    common_delete_sure: "您确定要永久删除此授权级别吗？",
    common_delete_not: "此授权级别正在使用中，无法删除。",
    common_password: "密码",
    common_required_password: "密码是必填的",
    common_format: "密码必须包含大小写字母、数字和特殊符号,且长度在8-64之间",
    common_mobile: "联系电话",
    common_required_mobile: "电话号码是必填的",
    common_mobile_enter: "请输入联系电话",
    common_mobile_format: "号码格式有误",
    common_comtract_id: "合同编号",
    common_comtract_id_enter: "请输入合同编号",
    common_comtract_id_format: "合同编号格式有误",
    common_comtract_id_not_null: "合同编号不能为空",
    common_comtract_amount: "合同金额",
    common_required_comtract_amount: "请输入合同金额",
    common_contract_unit: "合同单位",
    common_required_contract_unit: "请选择合同单位",
    common_query: "查询",
    common_reset: "重置",
    common_enter_mobile: "请输入手机号码",
    common_enter: "请输入",
    common_company_logo: "公司标志",
    common_maximum: "文件大小最大为5MB",
    common_upload: "上传",
    common_remove: "删除",
    common_allowed: "仅允许 JPEG, JPG, PNG",
    common_view: "预览",
    common_editor: "编辑",
    common_user_setting: "用户设置",
    common_delete: "删除",
    common_send_email: "发送邮件",
    common_union: "工会",
    common_confirm_password: "确认密码",
    common_required_confirm_password: "确认密码是必填的",
    common_confirm_password_format:
      "确认密码必须包含大小写字母、数字和特殊符号,且长度在8-64之间",
    common_confirm_password_not_match: "确认密码与密码不一致",
    common_enter_confirm_password: "请输入确认密码",
    common_success: "成功",
    common_failed: "失败",
    common_perpage: "每页记录数",
    common_change_password: "修改密码",
    common_no_content_found: "未找到内容.",
    common_click_add_content: `点击 ' + ' 添加内容`,
    common_language: "语言",
    my_profile: "我的资料",
    my_subscription: "订阅详情",
    about: "关于",
    logout: "退出",
    occupation: "职业",
    location: "位置",
    common_email: "邮箱",
    common_mobile: "手机号",
    enter_mobile: "请输入手机号",
    enter_email: "请输入邮箱",
    enter_occupation: "请输入职业",
    enter_location: "请输入位置",
    system_service: "显示系统服务指标",
    operation_time: "操作时间为每周7天*24小时",
    abnormal_exceed: "异常时间不超过每年8小时",
    reseponse_time: "服务响应时间不超过4小时",
    current_password: "当前密码",
    new_password: "新密码",
    enter_current_password: "请输入当前密码",
    enter_new_password: "请输入新密码",
    common_confirm_password: "确认密码",
    enter_confirm_password: "请输入确认密码",
    common_login: "登录",
    common_base_info: "基本信息",
    common_security: "安全设置",
    common_org_details: "组织详情",
    common_switch_org: "切换组织",
    common_org_list: "组织列表",
    common_principal_list: "负责人列表",
    common_no_data: "暂无内容",
    common_refresh_success: "刷新成功",
    common_enter_current_password: "请输入原始密码",
    common_enter_new_password: "请输入新密码",
    common_enter_confirm_password: "请输入确认密码",
    common_password_not_same: "两次密码不一致",
    file_type_error: "文件类型错误",
    common_handover: "切换",
  },

  errorCode: {
    G0000025: "未能获取到有效的令牌",
    G0000026: "登录账号已过期，请重新登录",
    default: "内部服务错误",
    networkError: "后端接口连接异常",
    timeout: "系统接口请求超时",
    error: "服务暂时不可用",
    data_repeat: "数据正在处理，请勿重复提交",
    400: "请求错误",
    401: "未授权，请重新登录",
    403: "拒绝访问",
    404: "请求地址出错",
    408: "请求超时",
    500: "服务器内部错误",
    501: "服务未实现",
    502: "网关错误",
    503: "服务不可用",
    504: "网关超时",
  },
};

export default common;
